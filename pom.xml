<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <artifactId>earnify</artifactId>
    <version>0.0.2-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>earnify</name>
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>earnify-common</module>
        <module>earnify-cms</module>
        <module>earnify-task</module>
        <module>earnify-async</module>
        <module>earnify-customer</module>
        <module>earnify-order</module>
        <module>mybatis-plus</module>
        <module>earnify-message</module>
    </modules>

    <parent>
        <groupId>com.letu.solutions</groupId>
        <artifactId>solutions-base</artifactId>
        <version>0.0.2-SNAPSHOT</version>
    </parent>
    <description>父管理模块</description>

    <properties>
        <project.build.sourceEncoding>utf-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>utf-8</project.reporting.outputEncoding>
        <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
        <skipTests>true</skipTests>
        <java.version>21</java.version>

        <depend.base.version>0.0.2-SNAPSHOT</depend.base.version>
<!--        <depend.base.version>0.0.1-SNAPSHOT</depend.base.version>-->
        <elasticsearch.version>7.10.0</elasticsearch.version>
<!--        <redisson-starter.version>3.23.1</redisson-starter.version>-->
        <mapstruct.version>1.5.2.Final</mapstruct.version>
    </properties>

    <dependencies>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <release>21</release>
                    <encoding>UTF-8</encoding>
                    <skip>true</skip>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- Source -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>oss</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <!-- 定义包含这些资源文件，能在jar包中获取这些文件 -->
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                </includes>
                <!--是否替换资源中的属性-->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <id>rdc</id>
            <distributionManagement>
                <repository>
                    <id>rdc-releases</id>
                    <url>https://packages.aliyun.com/maven/repository/2522531-release-z2FhSj/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <snapshotRepository>
                    <id>rdc-snapshots</id>
                    <url>https://packages.aliyun.com/maven/repository/2522531-snapshot-w6AbSq/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>
</project>