package com.letu.solutions.dubbo.earnify.customer;

import com.letu.solutions.core.enums.NotificationTemplateEnum;

import java.util.List;

/**
 * 站内信服务接口
 *
 * <AUTHOR>
 */
public interface MessageLetterFacade {

    /**
     * 发送站内信
     *
     * @param userId 用户ID
     * @param template 消息模板
     */
    void sendMessageLetter(Long userId, NotificationTemplateEnum template);

    /**
     * 批量发送站内信
     *
     * @param userIds 用户ID列表
     * @param template 消息模板
     */
    void batchSendMessageLetter(List<Long> userIds, NotificationTemplateEnum template);
}