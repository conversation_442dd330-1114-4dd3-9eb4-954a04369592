package com.letu.solutions.util.util;

import com.letu.solutions.model.dto.FinanceConfigCacheDTO;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class FinanceConfigCacheUtil {
    private static final String KEY_PREFIX = "finance:config:";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;




    public void setConfig(String network, FinanceConfigCacheDTO dto) {
        redisTemplate.opsForValue().set(KEY_PREFIX + network.toLowerCase(), dto);
    }

    public FinanceConfigCacheDTO getConfig(String network) {
        Object obj = redisTemplate.opsForValue().get(KEY_PREFIX + network.toLowerCase());
        if (obj instanceof FinanceConfigCacheDTO) {
            return (FinanceConfigCacheDTO) obj;
        }
        return null;
    }

    public void deleteConfig(String network) {
        redisTemplate.delete(KEY_PREFIX + network.toLowerCase());
    }
} 