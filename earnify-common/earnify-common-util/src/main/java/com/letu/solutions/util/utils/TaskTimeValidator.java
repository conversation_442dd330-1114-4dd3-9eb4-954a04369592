package com.letu.solutions.util.utils;

import com.letu.solutions.model.entity.cms.UserTask;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 任务时间校验工具类
 * 用于在用户操作时检查任务是否已超时
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
public class TaskTimeValidator {

    /**
     * 校验任务是否已到期超时
     * 
     * @param userTask 用户任务
     * @return 是否已超时
     */
    public static boolean isTaskExpired(UserTask userTask) {
        if (userTask == null || userTask.getTime() == null) {
            return false;
        }
        
        Date currentTime = new Date();
        boolean isExpired = currentTime.after(userTask.getTime());
        
        if (isExpired) {
            log.warn("任务{}已到期超时，到期时间：{}，当前时间：{}", 
                userTask.getId(), userTask.getTime(), currentTime);
        }
        
        return isExpired;
    }

    /**
     * 校验审核是否已超时
     * 
     * @param userTask 用户任务
     * @return 是否已超时
     */
    public static boolean isAuditExpired(UserTask userTask) {
        if (userTask == null || userTask.getExamineTime() == null) {
            return false;
        }
        
        Date currentTime = new Date();
        boolean isExpired = currentTime.after(userTask.getExamineTime());
        
        if (isExpired) {
            log.warn("任务{}审核已超时，审核到期时间：{}，当前时间：{}", 
                userTask.getId(), userTask.getExamineTime(), currentTime);
        }
        
        return isExpired;
    }

    /**
     * 校验申诉处理是否已超时
     * 
     * @param userTask 用户任务
     * @return 是否已超时
     */
    public static boolean isAppealProcessExpired(UserTask userTask) {
        if (userTask == null || userTask.getRejectTime() == null) {
            return false;
        }
        
        Date currentTime = new Date();
        boolean isExpired = currentTime.after(userTask.getRejectTime());
        
        if (isExpired) {
            log.warn("任务{}申诉处理已超时，申诉处理到期时间：{}，当前时间：{}", 
                userTask.getId(), userTask.getRejectTime(), currentTime);
        }
        
        return isExpired;
    }

    /**
     * 校验申诉有效期是否已超时
     * 
     * @param userTask 用户任务
     * @return 是否已超时
     */
    public static boolean isAppealValidExpired(UserTask userTask) {
        if (userTask == null || userTask.getAppealTime() == null) {
            return false;
        }
        
        Date currentTime = new Date();
        boolean isExpired = currentTime.after(userTask.getAppealTime());
        
        if (isExpired) {
            log.warn("任务{}申诉有效期已超时，申诉到期时间：{}，当前时间：{}", 
                userTask.getId(), userTask.getAppealTime(), currentTime);
        }
        
        return isExpired;
    }

    /**
     * 根据任务状态校验是否已超时
     * 
     * @param userTask 用户任务
     * @return 是否已超时
     */
    public static boolean isTaskTimeout(UserTask userTask) {
        if (userTask == null) {
            return false;
        }
        
        UserTaskStatusEnum status = userTask.getState();
        
        switch (status) {
            case incomplete:
                return isTaskExpired(userTask);
            case pendingApproval:
                return isAuditExpired(userTask);
            case partyARejects:
                return isAppealProcessExpired(userTask);
            case appealInProgress:
                return isAppealValidExpired(userTask);
            default:
                return false;
        }
    }

    /**
     * 获取超时原因描述
     * 
     * @param userTask 用户任务
     * @return 超时原因描述
     */
    public static String getTimeoutReason(UserTask userTask) {
        if (userTask == null) {
            return "任务不存在";
        }
        
        UserTaskStatusEnum status = userTask.getState();
        
        switch (status) {
            case incomplete:
                return isTaskExpired(userTask) ? "任务已到期超时" : null;
            case pendingApproval:
                return isAuditExpired(userTask) ? "审核已超时" : null;
            case partyARejects:
                return isAppealProcessExpired(userTask) ? "申诉处理已超时" : null;
            case appealInProgress:
                return isAppealValidExpired(userTask) ? "申诉有效期已超时" : null;
            default:
                return null;
        }
    }
} 