package com.letu.solutions.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 站内信消息模板
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum  NotificationTemplateEnum {
    TASK_COMPLETED("任务完成", "恭喜您，您有任务已完成！请及时查看任务奖励"),
    TASK_FAILED("任务失败", "抱歉，您有任务未能完成，无法获得奖励"),
    TASK_REVIEW_REJECTED("审核结果：不通过", "抱歉，您有任务未能通过审核，请及时查看审核结果"),
    WITHDRAW_APPROVED("提现通过", "恭喜您！您的提现申请已通过审核，请及时去钱包查验"),
    WITHDRAW_REJECTED("提现失败", "抱歉，您的提现申请未通过审核，请检查提现申请信息后重新提现");

    private final String title;
    private final String content;
}
