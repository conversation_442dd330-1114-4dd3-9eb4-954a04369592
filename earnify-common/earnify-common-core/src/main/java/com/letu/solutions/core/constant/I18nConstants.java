package com.letu.solutions.core.constant;

/**
 * @Author: jack ma
 * @CreateTime: 2025-05-12  14:14
 * @Version: 1.0
 */
public interface I18nConstants {
    String VERIFIED_CODE_ERR = "$验证码错误,请重新输入";

    String INVALID_GOOGLE_TOKEN = "$无效的谷歌Token";

    String GOOGLE_VERIFICATION_FAIL = "$谷歌登录验证失败";

    String ACCOUNT_DISABLE = "$账号已被禁用,若有疑问请联系客服咨询";

    String ACCOUNT_CANCEL = "$账号已注销";

    String PLATFORM_NOT_SUPPORT = "$当前平台不支持";

    // 任务相关错误信息
    String TASK_NOT_FOUND = "$任务不存在";
    
    String TASK_ALREADY_RECEIVED = "$任务已被领取";
    
    String TASK_RECEIVE_LIMIT_EXCEEDED = "$任务领取数量已达上限";
    
    String USER_TASK_NOT_FOUND = "$用户任务不存在";
    
    String USER_TASK_STEP_NOT_FOUND = "$任务步骤不存在";
    
    String TASK_ALREADY_COMPLETED = "$任务已完成";
    
    String TASK_ALREADY_SUBMITTED = "$任务已提交";
    
    String TASK_ALREADY_APPEALED = "$任务已申诉";
    
    String TASK_NOT_COMPLETED = "$任务未完成，无法提交";
    
    String TASK_STEPS_NOT_ALL_SAVED = "$任务步骤未全部保存，无法提交";
    
    String TASK_EXPIRED = "$任务已过期";
    
    String TASK_NOT_IN_PROGRESS = "$任务状态不正确，无法操作";
    
    String TASK_ID_REQUIRED = "$任务ID不能为空";
    
    String USER_TASK_ID_REQUIRED = "$用户任务ID不能为空";
    
    String USER_TASK_STEP_ID_REQUIRED = "$任务步骤ID不能为空";
    
    String USER_ID_REQUIRED = "$用户ID不能为空";
    
    String TASK_OPERATION_FAILED = "$任务操作失败";
    
    String TASK_RECEIVE_FAILED = "$任务领取失败";
    
    String TASK_SUBMIT_FAILED = "$任务提交失败";
    
    String TASK_APPEAL_FAILED = "$任务申诉失败";
    
    String TASK_STEP_SAVE_FAILED = "$任务步骤保存失败";
    
    String TASK_DETAIL_NOT_FOUND = "$任务详情不存在";
    
    String TASK_PARTY_USER_INFO_NOT_FOUND = "$任务发布者信息不存在";

    // 新增的常量
    String TASK_RECEIVE_SUCCESS = "$任务领取成功";
    
    String APPEAL_FAILED_NO_PERMISSION = "$申诉失败，任务不存在或无权限";
    
    String APPEAL_FAILED_STATUS_NOT_ALLOWED = "$只有甲方驳回的任务才能申诉";
    
    String APPEAL_SUCCESS = "$申诉成功";
    
    String APPEAL_FAILED = "$申诉失败";
    
    String TASK_NOT_EXIST_OR_NO_PERMISSION = "$任务不存在或无权限";
    
    String PLEASE_SAVE_ALL_STEPS_BEFORE_SUBMITTING = "$请先保存所有步骤后再提交";
    
    String SUBMIT_SUCCESS = "$提交成功";
    
    String PARAMETERS_INCOMPLETE = "$参数不完整";
    
    String TASK_NOT_IN_PROGRESS_NO_MORE_OPERATIONS = "$任务不在进行中，无需再操作步骤";
    
    String STEP_NOT_EXIST_OR_NO_PERMISSION = "$步骤不存在或无权限";
    
    String STEP_COMPLETED = "$步骤已完成";
    
    String STEP_ALREADY_COMPLETED = "$步骤已完成，无需重复操作";
    
    String STEP_IDENTIFIER_REQUIRED = "$步骤标识不能为空，请提供步骤排序或步骤ID";

    // 任务超时相关错误信息
    String TASK_EXPIRED_CANNOT_APPEAL = "$任务已超时，无法申诉";
    
    String TASK_EXPIRED_CANNOT_SUBMIT = "$任务已超时，无法提交";
    
    String TASK_EXPIRED_CANNOT_CONTINUE = "$任务已超时，无法继续操作";
    
    String TASK_EXPIRED_CANNOT_REVIEW = "$任务已超时，无法审核";

    // 用户相关错误信息
    String USER_NOT_FOUND = "$用户不存在";
    
    String USER_DISABLED = "$用户已被禁用";
    
    String USER_UPDATE_FAILED = "$用户信息更新失败";
    
    String USER_HEAD_UPDATE_FAILED = "$用户头像更新失败";
    
    String USER_NAME_UPDATE_FAILED = "$用户昵称更新失败";
    
    String USER_BASE_UPDATE_FAILED = "$用户基础信息更新失败";
    
    String USER_HEAD_UPDATE_SUCCESS = "$用户头像更新成功";
    
    String USER_NAME_UPDATE_SUCCESS = "$用户昵称更新成功";
    
    String USER_BASE_UPDATE_SUCCESS = "$用户基础信息更新成功";
    
    String NICKNAME_CONTAINS_VIOLATION_WORDS = "$昵称包含违规词汇，请重新命名";
    
    String HEAD_IMAGE_URL_INVALID = "$头像地址不合法";
    
    String HEAD_IMAGE_URL_EMPTY = "$头像地址不能为空";
    
    String NICKNAME_EMPTY = "$用户昵称不能为空";
    
    String NICKNAME_INVALID = "$用户昵称不合法";

    // 邮件相关错误信息
    String EMAIL_EMPTY = "$邮箱不能为空";
    
    String EMAIL_INVALID = "$邮箱格式不合法";
    
    String EMAIL_CODE_EMPTY = "$邮箱验证码不能为空";
    
    String EMAIL_CODE_INVALID = "$邮箱验证码格式不合法";
    
    String EMAIL_CODE_EXPIRED = "$邮箱验证码已过期";
    
    String EMAIL_CODE_ERROR = "$邮箱验证码错误";
    
    String EMAIL_SEND_FAILED = "$邮件发送失败";
    
    String EMAIL_SEND_SUCCESS = "$邮件发送成功";
    
    String EMAIL_CONFIG_INCOMPLETE = "$邮件配置不完整";
    
    String EMAIL_FREQUENCY_LIMIT = "$邮件发送频率过高，请稍后重试";
    
    String EMAIL_ACCOUNT_LOCKED = "$邮箱账户已被锁定，请稍后重试";

    // ==================== 邀请相关 ====================

    /**
     * 被邀请人已注册
     */
    String INVITEE_ALREADY_REGISTERED = "$被邀请人已注册";

    /**
     * 已邀请过该手机号
     */
    String ALREADY_INVITED_THIS_PHONE = "$已邀请过该手机号";

    /**
     * 邀请创建失败
     */
    String INVITE_CREATE_FAILED = "$邀请创建失败";

    /**
     * 邀请创建成功
     */
    String INVITE_CREATE_SUCCESS = "$邀请创建成功";

    /**
     * 邀请码不存在或无效
     */
    String INVITE_CODE_INVALID = "$邀请码不存在或无效";

    /**
     * 邀请码对应的用户已被禁用
     */
    String INVITE_CODE_USER_DISABLED = "$邀请码对应的用户已被禁用";

    /**
     * 不能邀请自己
     */
    String CANNOT_INVITE_SELF = "$不能邀请自己";

    /**
     * 已经被邀请过
     */
    String ALREADY_INVITED = "$您已经被邀请过，不能再次使用邀请码";

    /**
     * 获取邀请码失败
     */
    String INVITE_CODE_GET_FAILED = "$获取邀请码失败";

    // ==================== 社交媒体绑定相关 ====================

    /**
     * 社交媒体已绑定
     */
    String SOCIAL_ALREADY_BOUND = "$该社交媒体已绑定";

    /**
     * 社交媒体绑定成功
     */
    String SOCIAL_BIND_SUCCESS = "$社交媒体绑定成功";

    /**
     * 社交媒体绑定失败
     */
    String SOCIAL_BIND_FAILED = "$社交媒体绑定失败";

    // ==================== 钱包绑定相关 ====================

    /**
     * 钱包已绑定
     */
    String WALLET_ALREADY_BOUND = "$该钱包地址已绑定";

    /**
     * 钱包绑定成功
     */
    String WALLET_BIND_SUCCESS = "$钱包绑定成功";

    /**
     * 钱包绑定失败
     */
    String WALLET_BIND_FAILED = "$钱包绑定失败";

    /**
     * 钱包不存在
     */
    String WALLET_NOT_FOUND = "$钱包不存在";

    /**
     * 设置默认钱包失败
     */
    String WALLET_SET_DEFAULT_FAILED = "$设置默认钱包失败";

    /**
     * 删除钱包失败
     */
    String WALLET_DELETE_FAILED = "$删除钱包失败";

    // ==================== 手机号绑定相关 ====================

    /**
     * 手机号已绑定
     */
    String PHONE_ALREADY_BOUND = "$手机号已被绑定";

    /**
     * 手机号绑定成功
     */
    String PHONE_BIND_SUCCESS = "$手机号绑定成功";

    /**
     * 手机号绑定失败
     */
    String PHONE_BIND_FAILED = "$手机号绑定失败";

    /**
     * 手机号格式不正确
     */
    String PHONE_FORMAT_INVALID = "$手机号格式不正确";

    /**
     * 验证码发送成功
     */
    String SMS_CODE_SEND_SUCCESS = "$验证码发送成功";

    /**
     * 验证码发送失败
     */
    String SMS_CODE_SEND_FAILED = "$验证码发送失败";

    /**
     * 验证码错误
     */
    String SMS_CODE_ERROR = "$验证码错误";

    /**
     * 验证码已过期
     */
    String SMS_CODE_EXPIRED = "$验证码已过期";

    /**
     * 验证码不能为空
     */
    String SMS_CODE_EMPTY = "$验证码不能为空";

    /**
     * 手机号不能为空
     */
    String PHONE_EMPTY = "$手机号不能为空";

    /**
     * 已绑定手机号，不可切换
     */
    String PHONE_ALREADY_BOUND_CANNOT_SWITCH = "$已绑定手机号，不可切换";

    /**
     * 用户已注册或验证码有误
     */
    String USER_ALREADY_REGISTERED_OR_CODE_ERROR = "$用户已注册或验证码有误";

}
