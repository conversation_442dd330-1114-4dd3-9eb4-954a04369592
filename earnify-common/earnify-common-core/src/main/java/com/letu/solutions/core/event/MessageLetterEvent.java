package com.letu.solutions.core.event;

import com.letu.solutions.core.enums.NotificationTemplateEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 站内信事件
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageLetterEvent {

    /**
     * 用户ID列表
     */
    private List<Long> userIds;

    /**
     * 消息模板
     */
    private NotificationTemplateEnum template;

    /**
     * 单用户构造器
     */
    public MessageLetterEvent(Long userId, NotificationTemplateEnum template) {
        this.userIds = List.of(userId);
        this.template = template;
    }
}