package com.letu.solutions.model.customer.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户账户余额响应
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountBalanceRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 可用余额
     */
    private BigDecimal availableAmount;

    /**
     * 冻结余额
     */
    private BigDecimal frozenAmount;

    /**
     * 总余额（可用+冻结）
     */
    private BigDecimal totalAmount;
}
