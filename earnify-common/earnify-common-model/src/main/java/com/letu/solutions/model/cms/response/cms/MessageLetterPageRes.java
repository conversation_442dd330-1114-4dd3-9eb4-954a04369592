package com.letu.solutions.model.cms.response.cms;

import com.letu.solutions.core.enums.MessageLetterStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 站内信分页响应
 *
 * <AUTHOR>
 */
@Data
public class MessageLetterPageRes {

    /**
     * 站内信ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 阅读状态
     */
    private MessageLetterStatusEnum readStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}