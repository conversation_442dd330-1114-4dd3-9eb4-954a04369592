package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 财务配置表
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class FinanceConfigUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    @NotNull(message = "不可为空")
    private Long id;
    /**
     * 链类型（TRC20、ERC20）
     */
    @NotEmpty(message = "链类型（TRC20、ERC20）不可为空")
    private String network;
    /**
     * 最小充值金额
     */
    private BigDecimal minAmount;
    /**
     * 最大充值金额
     */
    private BigDecimal maxAmount;
    /**
     * 最小提现金额
     */
    private BigDecimal minWithdraw;
    /**
     * 最大提现金额
     */
    private BigDecimal maxWithdraw;
    /**
     * 单用户每日最多可提现次数
     */
    private Integer maxWithdrawTimesPerDay;
    /**
     * 手续费比例（如 0.005 表示0.5%）
     */
    private BigDecimal feeRate;
    /**
     * 每笔提现固定手续费
     */
    private BigDecimal fixedFee;

    /**
     * 平台钱包地址列表
     */
    private List<String> addressList;
}
