package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.enums.MessageLetterStatusEnum;
import com.letu.solutions.model.entity.cms.MessageLetter;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 站内信列表查询请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageLetterListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站内信ID
     */
    private Long id;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 阅读状态
     */
    private MessageLetterStatusEnum readStatus;

}