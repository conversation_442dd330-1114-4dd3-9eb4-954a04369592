package com.letu.solutions.model.cms.response.cms;

import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TaskStepDTO implements Serializable {
    /** 步骤序号（如：步骤一、步骤二） */
    private Integer stepIndex;
    /** 步骤描述 */
    private String stepDesc;
    /** 用户提交的文字凭证 */
    private String taskTextOperate;
    /** 用户提交的图片凭证 */
    private List<String> taskImageOperate;
    /** 是否保存，0-未保存，1-已保存 */
    private Integer saved;
}