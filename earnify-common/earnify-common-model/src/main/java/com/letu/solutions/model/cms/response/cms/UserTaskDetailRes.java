package com.letu.solutions.model.cms.response.cms;

import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 任务详情响应，包含任务信息和步骤明细
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTaskDetailRes implements Serializable {
    private static final long serialVersionUID = 1L;


    /** 关联产品名称 */
    private String productName;
    /** 任务属性 1人工 2自动 */
    private Integer taskAttribute;
    /** 任务类型 */
    private TaskTypeEnum taskType;
    /** 任务奖励 */
    private BigDecimal price;
    /** 任务状态 */
    private TaskStatusEnum taskState;


    // 步骤完成统计
    /** 步骤完成统计（如 2/10） */
    private String stepStatusSummary;
    // 任务步骤列表
    private List<TaskStepDTO> steps;

}
