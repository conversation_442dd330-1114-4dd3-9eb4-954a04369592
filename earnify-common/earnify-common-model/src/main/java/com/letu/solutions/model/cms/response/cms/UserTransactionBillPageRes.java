package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.FrozenSideTypeEnum;
import lombok.Data;
import java.io.Serializable;

/**
 * 账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTransactionBillPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 账号类型（甲方，乙方）
     */
    private AccountTypeEnum accountType;
    /**
     * 动账类型（任务奖励/保证金冻结，任务奖励冻结，任务奖励到账，任务奖励解冻发放，任务奖励/保证金解冻返还，任务奖励解冻返还，充值，提现，提现审核驳回）
     */
    private FundTypeEnum fundType;
    /**
     * 方向（入账，出账）
     */
    private FundSideTypeEnum side;

    /**
     * 冻结方向类型（冻结，解冻）
     */
    private FrozenSideTypeEnum frozenSide;
    /**
     * 动账金额
     */
    private BigDecimal amount;
    /**
     * 币种
     */
    private String coinSymbol;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 变动前金额
     */
    private BigDecimal balanceBefore;
    /**
     * 变动后金额
     */
    private BigDecimal balanceAfter;
    /**
     * 冻结金额
     */
    private BigDecimal frozen;
    /**
     * 充值或提现关联id
     */
    private Long fundId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 当前可用金额
     */
    private BigDecimal currentAvailableAmount;
    /**
     * 当前冻结金额
     */
    private BigDecimal currentFrozenAmount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
