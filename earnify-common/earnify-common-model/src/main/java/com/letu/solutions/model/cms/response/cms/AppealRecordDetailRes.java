package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 申述记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class AppealRecordDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 甲方用户ID
     */
    private Long partyAUserId;
    /**
     * 甲方用户名
     */
    private String partyAUsername;
    /**
     * 乙方用户ID
     */
    private Long partyBUserId;
    /**
     * 乙方用户名
     */
    private String partyBUsername;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 任务属性
     */
    private Integer taskAttribute;
    /**
     * 任务类型
     */
    private Integer taskType;
    /**
     * 任务完成时限
     */
    private Date taskDeadline;
    /**
     * 申述状态（0待审核，1通过，2不通过
     */
    private Integer appealStatus;
    /**
     * 任务积分
     */
    private BigDecimal taskPoints;
    /**
     * 已发放积分
     */
    private BigDecimal grantedPoints;
    /**
     * 审核说明
     */
    private String reviewRemark;
    /**
     * 相关凭证，图片地址数组（JSON 格式）
     */
    private List<String> evidenceImages;
    /**
     * 结束时间
     */
    private Date endTime;
}
