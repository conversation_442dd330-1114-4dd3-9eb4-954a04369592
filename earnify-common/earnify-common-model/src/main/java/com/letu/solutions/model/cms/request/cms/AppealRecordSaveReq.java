package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 申述记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class AppealRecordSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 日期（8位数字，格式：YYYYMMDD）
     */
    private Integer day;
    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不可为空")
    private Long taskId;
    /**
     * 甲方用户ID
     */
    @NotNull(message = "甲方用户ID不可为空")
    private Long partyAUserId;
    /**
     * 甲方用户名
     */
    private String partyAUsername;
    /**
     * 乙方用户ID
     */
    @NotNull(message = "乙方用户ID不可为空")
    private Long partyBUserId;
    /**
     * 乙方用户名
     */
    private String partyBUsername;
    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不可为空")
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 任务属性
     */
    private Integer taskAttribute;
    /**
     * 任务类型
     */
    private Integer taskType;
    /**
     * 任务完成时限
     */
    private Date taskDeadline;
    /**
     * 申述状态（0待审核，1通过，2不通过
     */
    private Integer appealStatus;
    /**
     * 任务积分
     */
    private BigDecimal taskPoints;
    /**
     * 已发放积分
     */
    private BigDecimal grantedPoints;
    /**
     * 审核说明
     */
    private String reviewRemark;
    /**
     * 相关凭证，图片地址数组（JSON 格式）
     */
    private String evidenceImages;
    /**
     * 结束时间
     */
    private Date endTime;
}
