package com.letu.solutions.model.cms.response.cms;

import lombok.Data;

import java.util.Date;

/**
 * 站内信详情响应
 *
 * <AUTHOR>
 */
@Data
public class MessageLetterDetailRes {

    /**
     * 站内信ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 阅读状态
     */
    private String readStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}