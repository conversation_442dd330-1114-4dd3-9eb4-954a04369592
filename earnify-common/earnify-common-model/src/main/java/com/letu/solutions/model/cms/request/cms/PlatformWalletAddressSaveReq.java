package com.letu.solutions.model.cms.request.cms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 平台钱包地址表
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatformWalletAddressSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 链名称（如 TRC20、ERC20、BEP20）
     */
    @JsonProperty("network")
    private String network;

    /**
     * 平台钱包地址
     */
    @NotEmpty(message = "平台钱包地址不可为空")
    @JsonProperty("address")
    private String address;
}
