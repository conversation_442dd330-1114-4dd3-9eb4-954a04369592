package com.letu.solutions.message.controller;

import com.letu.solutions.core.model.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 消息/用户相关查询
 *
 * <AUTHOR>
 * @date 2023年03月22日 9:48
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class UserController {

    /**
     * 测试汉语话
     *
     */
    @GetMapping("/user/test.e")
    public R<String> test() {
        return R.success("123");
    }

}
