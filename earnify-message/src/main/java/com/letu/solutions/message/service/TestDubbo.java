package com.letu.solutions.message.service;

import com.letu.solutions.dubbo.earnify.customer.CustomerFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.validation.MethodValidated;

@DubboService
@Slf4j
@RequiredArgsConstructor
public class TestDubbo implements CustomerFacade {

    @Override
    @MethodValidated
    public String test() {
        return "test6666";
    }
}
