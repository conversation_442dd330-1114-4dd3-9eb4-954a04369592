package com.letu.solutions.message.listener;

import com.letu.solutions.core.event.MessageLetterEvent;
import com.letu.solutions.message.service.MessageLetterHandleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 站内信事件监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageLetterEventListener {

    private final MessageLetterHandleService MessageLetterHandleService;

    @Async("asyncPool")
    @EventListener
    public void handleMessageLetterEvent(MessageLetterEvent event) {
        log.info("处理站内信事件，用户数量：{}，模板：{}", event.getUserIds().size(), event.getTemplate());
        try {
            MessageLetterHandleService.handleMessageLetter(event);
            log.info("站内信事件处理完成");
        } catch (Exception e) {
            log.error("站内信事件处理失败", e);
        }
    }
}