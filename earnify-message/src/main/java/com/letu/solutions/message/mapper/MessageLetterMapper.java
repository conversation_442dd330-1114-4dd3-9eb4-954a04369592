package com.letu.solutions.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.letu.solutions.model.entity.cms.MessageLetter;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 站内信Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MessageLetterMapper extends BaseMapper<MessageLetter> {

    /**
     * 批量插入站内信
     */
    @Insert("""
            <script>
            INSERT INTO message_letter (user_id, title, content, read_status, create_time, update_time)
            VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.userId}, #{item.title}, #{item.content}, #{item.readStatus}, #{item.createTime}, #{item.updateTime})
            </foreach>
            </script>
            """)
    int batchInsert(@Param("list") List<MessageLetter> messages);
}