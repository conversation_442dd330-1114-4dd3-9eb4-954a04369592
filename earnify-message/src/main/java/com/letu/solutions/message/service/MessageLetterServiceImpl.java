package com.letu.solutions.message.service;

import com.letu.solutions.core.event.MessageLetterEvent;
import com.letu.solutions.core.enums.NotificationTemplateEnum;
import com.letu.solutions.dubbo.earnify.customer.MessageLetterFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 站内信服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class MessageLetterServiceImpl implements MessageLetterFacade {

    private final ApplicationEventPublisher eventPublisher;

    @Override
    public void sendMessageLetter(Long userId, NotificationTemplateEnum template) {
        log.info("发送站内信，用户ID：{}，模板：{}", userId, template);
        eventPublisher.publishEvent(new MessageLetterEvent(userId, template));
    }

    @Override
    public void batchSendMessageLetter(List<Long> userIds, NotificationTemplateEnum template) {
        log.info("批量发送站内信，用户数量：{}，模板：{}", userIds.size(), template);
        eventPublisher.publishEvent(new MessageLetterEvent(userIds, template));
    }
}