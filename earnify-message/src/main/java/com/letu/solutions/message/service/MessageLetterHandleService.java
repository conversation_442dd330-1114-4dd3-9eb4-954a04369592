package com.letu.solutions.message.service;

import com.letu.solutions.core.event.MessageLetterEvent;
import com.letu.solutions.message.mapper.MessageLetterMapper;
import com.letu.solutions.model.entity.cms.MessageLetter;
import com.letu.solutions.core.enums.MessageLetterStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * 站内信处理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageLetterHandleService {

    private final MessageLetterMapper MessageLetterMapper;
    private final TransactionTemplate transactionTemplate;

    public void handleMessageLetter(MessageLetterEvent event) {
        Assert.notNull(event, "站内信事件不能为空");
        Assert.notEmpty(event.getUserIds(), "用户ID列表不能为空");
        Assert.notNull(event.getTemplate(), "消息模板不能为空");

        transactionTemplate.execute(status -> {
            List<MessageLetter> messages = event.getUserIds().stream()
                    .map(userId -> buildMessageLetter(userId, event))
                    .toList();

            int insertCount = MessageLetterMapper.batchInsert(messages);
            log.info("批量插入站内信完成，插入数量：{}，预期数量：{}", insertCount, messages.size());
            
            Assert.isTrue(insertCount == messages.size(), "站内信插入数量不匹配");
            return true;
        });
    }

    private MessageLetter buildMessageLetter(Long userId, MessageLetterEvent event) {
        return MessageLetter.builder()
                .userId(userId)
                .title(event.getTemplate().getTitle())
                .content(event.getTemplate().getContent())
                .readStatus(MessageLetterStatusEnum.unread.getCode())
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }
}