server:
  port: 11805
  servlet:
    context-path: /earnify-message
dubbo:
  protocol:
    port: 21805
  cloud:
    subscribed-services: 'middle-cms'
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: earnify-message
    type: message
  cloud:
    nacos:
      server-addr: http://192.168.77.101:28848
      discovery:
        namespace: ${discoverNameSpace:server}
        username: earnify
        password: 6sqGL8nj4oIt5P8n
      config:
        file-extension: yml
        namespace: earnify
        username: earnify
        password: 6sqGL8nj4oIt5P8n
  config:
    import:
      - nacos:redis.yaml
      - nacos:mysql.yaml
      - nacos:mybatis.yaml
      - nacos:base.yaml
      - nacos:nos.yaml
      - nacos:actuator.yaml
      - nacos:dubbo${discoverDubboEnv:}.yaml
      - nacos:account.yaml
      - nacos:oss.yaml?refresh=true
      - nacos:refresh.yaml?refresh=true
      - nacos:pay.yaml
      - nacos:check.yaml
