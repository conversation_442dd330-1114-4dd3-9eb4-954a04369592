<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.UserTaskMapper">




    <select id="sumPriceByTaskIdAndStates" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(price),0) FROM user_task
        WHERE task_id = #{taskId}
        <if test="states != null and states.size > 0">
            AND (
            <foreach collection="states" item="item" index="index" open="" separator=" or " close="">
                <choose>
                    <when test="item == 'partyARejects'">
                        (state = #{item} and NOW() &lt;= appeal_time)
                    </when>
                    <otherwise>
                        state = #{item}
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>
    </select>

    <resultMap id="UserTaskPageResMap" type="com.letu.solutions.model.cms.response.cms.UserTaskPageRes">
        <result property="taskId" column="id"/>
        <result property="state" column="state"/>
        <result property="taskName" column="task_name"/>
        <result property="partyANickName" column="party_a_nick_name"/>
        <result property="partyAId" column="party_a_id"/>
        <result property="partyBNickName" column="party_b_nick_name"/>
        <result property="partyBId" column="party_b_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="attribute" column="attribute"/>
        <result property="taskType" column="task_type"/>
        <result property="weightSorting" column="weight_sorting"/>
        <result property="progress" column="progress"/>
        <result property="time" column="time"/>
        <result property="price" column="price"/>
        <result property="createTime" column="create_time"/>
        <result property="examineTime" column="examine_time"/>
    </resultMap>
    <resultMap id="UserTaskExcelMap" type="com.letu.solutions.cms.dto.UserTaskExcel">
        <result property="taskId" column="id"/>
        <result property="state" column="state"/>
        <result property="taskName" column="task_name"/>
        <result property="partyANickName" column="party_a_nick_name"/>
        <result property="partyAId" column="party_a_id"/>
        <result property="partyBNickName" column="party_b_nick_name"/>
        <result property="partyBId" column="party_b_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="attribute" column="attribute"/>
        <result property="taskType" column="task_type"/>
        <result property="weightSorting" column="weight_sorting"/>
        <result property="progress" column="progress"/>
        <result property="time" column="time"/>
        <result property="price" column="price"/>
        <result property="createTime" column="create_time"/>
        <result property="examineTime" column="examine_time"/>
    </resultMap>
    <select id="selectUserTaskPage" resultMap="UserTaskPageResMap">
        SELECT
        t.id,
        t.name AS task_name,
        partyA.nick_name AS party_a_nick_name,
        partyA.id AS party_a_id,
        partyB.nick_name AS party_b_nick_name,
        partyB.id AS party_b_id,
        t.product_id,
        p.product_name,
        t.attribute,
        t.task_type,
        t.weight_sorting,
        ut.state,
        ut.create_time,
        ut.examine_time,
        ut.appeal_time,
        t.time,
        t.price,
        CONCAT(
        (SELECT COUNT(1) FROM user_task_step ut2 WHERE ut2.task_id = t.id AND ut2.user_id = ut.user_id AND ut2.is_saved = 1),
        '/',
        (SELECT COUNT(1) FROM user_task_step ut3 WHERE ut3.task_id = t.id AND ut3.user_id = ut.user_id)
        ) AS progress
        FROM task t
        inner JOIN user_task ut ON ut.task_id = t.id
        LEFT JOIN product p ON t.product_id = p.id
        LEFT JOIN `user` partyA ON t.user_id = partyA.id
        LEFT JOIN `user` partyB ON ut.user_id = partyB.id
        <where>
            <if test="req.taskName != null and req.taskName != ''">
                AND t.name LIKE CONCAT('%', #{req.taskName}, '%')
            </if>
            <if test="req.taskId != null">
                AND t.id = #{req.taskId}
            </if>
            <if test="req.partyANickName != null and req.partyANickName != ''">
                AND partyA.nick_name LIKE CONCAT('%', #{req.partyANickName}, '%')
            </if>
            <if test="req.partyAId != null">
                AND partyA.id = #{req.partyAId}
            </if>
            <if test="req.partyBNickName != null and req.partyBNickName != ''">
                AND partyB.nick_name LIKE CONCAT('%', #{req.partyBNickName}, '%')
            </if>
            <if test="req.partyBId != null">
                AND partyB.id = #{req.partyBId}
            </if>
            <if test="req.productId != null">
                AND t.product_id = #{req.productId}
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.staTime != null and req.staTime !=''">
                AND ut.create_time >= #{req.staTime}
            </if>
            <if test="req.finishTime != null and req.finishTime !=''">
                AND ut.create_time &lt;= #{req.finishTime}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="selectUserTaskList" resultMap="UserTaskExcelMap">
        SELECT
        t.id,
        t.name AS task_name,
        partyA.nick_name AS party_a_nick_name,
        partyA.id AS party_a_id,
        partyB.nick_name AS party_b_nick_name,
        partyB.id AS party_b_id,
        t.product_id,
        p.product_name,
        t.attribute,
        t.task_type,
        t.weight_sorting,
        ut.state,
        ut.create_time,
        ut.examine_time,
        ut.appeal_time,
        t.time,
        t.price,
        CONCAT(
        (SELECT COUNT(1) FROM user_task_step ut2 WHERE ut2.task_id = t.id AND ut2.user_id = ut.user_id AND ut2.is_saved = 1),
        '/',
        (SELECT COUNT(1) FROM user_task_step ut3 WHERE ut3.task_id = t.id AND ut3.user_id = ut.user_id)
        ) AS progress
        FROM task t
        inner JOIN user_task ut ON ut.task_id = t.id
        LEFT JOIN product p ON t.product_id = p.id
        LEFT JOIN `user` partyA ON t.user_id = partyA.id
        LEFT JOIN `user` partyB ON ut.user_id = partyB.id
        <where>
            <if test="req.taskName != null and req.taskName != ''">
                AND t.name LIKE CONCAT('%', #{req.taskName}, '%')
            </if>
            <if test="req.taskId != null">
                AND t.id = #{req.taskId}
            </if>
            <if test="req.partyANickName != null and req.partyANickName != ''">
                AND partyA.nick_name LIKE CONCAT('%', #{req.partyANickName}, '%')
            </if>
            <if test="req.partyAId != null">
                AND partyA.id = #{req.partyAId}
            </if>
            <if test="req.partyBNickName != null and req.partyBNickName != ''">
                AND partyB.nick_name LIKE CONCAT('%', #{req.partyBNickName}, '%')
            </if>
            <if test="req.partyBId != null">
                AND partyB.id = #{req.partyBId}
            </if>
            <if test="req.productId != null">
                AND t.product_id = #{req.productId}
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.staTime != null and req.staTime !=''">
                AND ut.create_time >= #{req.staTime}
            </if>
            <if test="req.finishTime != null and req.finishTime !=''">
                AND ut.create_time &lt;= #{req.finishTime}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <resultMap id="UserTaskDetailWithStepsMap" type="com.letu.solutions.model.cms.response.cms.UserTaskDetailRes">
        <result property="productName" column="product_name"/>
        <result property="taskAttribute" column="task_attribute"/>
        <result property="taskType" column="task_type"/>
        <result property="taskState" column="state"/>
        <result property="price" column="task_price"/>
        <collection property="steps" ofType="com.letu.solutions.model.cms.response.cms.TaskStepDTO">
            <result property="stepIndex" column="step_index"/>
            <result property="stepDesc" column="step_desc"/>
            <result property="saved" column="saved"/>
            <result property="taskTextOperate" column="task_text_operate"/>
            <result property="taskImageOperate" column="task_image_operate" typeHandler="com.letu.solutions.model.hanlder.StringListCommaTypeHandler"/>
        </collection>
    </resultMap>

    <select id="selectUserTaskDetailWithSteps" resultMap="UserTaskDetailWithStepsMap">
        SELECT
            p.product_name,
            t.attribute AS task_attribute,
            t.task_type,
            t.state,
            t.price AS task_price,
            ut.srot AS step_index,
            ut.task_describe AS step_desc,
            ut.task_text_operate,
            ut.task_image_operate,
            ut.is_saved as saved
        FROM task t
        LEFT JOIN user_task_step ut ON ut.task_id = t.id
        LEFT JOIN product p ON t.product_id = p.id
        WHERE ut.task_id = #{taskId}
        <if test="partyBUserId != null and partyBUserId != ''">
            and ut.user_id=#{partyBUserId}
        </if>
        ORDER BY ut.srot ASC
    </select>
</mapper>
