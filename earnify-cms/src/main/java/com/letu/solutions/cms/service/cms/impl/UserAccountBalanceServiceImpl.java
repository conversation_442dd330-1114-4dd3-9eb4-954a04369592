package com.letu.solutions.cms.service.cms.impl;

import com.letu.solutions.cms.service.user.UserService;
import com.letu.solutions.model.cms.response.user.UserDetailRes;
import com.letu.solutions.model.customer.response.AccountBalanceRes;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.cms.mapper.cms.UserAccountBalanceMapper;
import com.letu.solutions.cms.service.cms.UserAccountBalanceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.util.constants.CurrencyConstants;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceListReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceSaveReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserAccountBalancePageRes;
import com.letu.solutions.model.cms.response.cms.UserAccountBalanceDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import org.springframework.util.Assert;

/**
 * 账户资金信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserAccountBalanceServiceImpl extends ServiceImpl<UserAccountBalanceMapper, UserAccountBalance> implements UserAccountBalanceService {
    private final UserService userService;
    @Override
    public Page<UserAccountBalancePageRes> selectBasicPage(Page<UserAccountBalance> page, UserAccountBalanceListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),UserAccountBalance::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getUserId()),UserAccountBalance::getUserId, request.getUserId())
                .eq(ObjectUtil.isNotEmpty(request.getAvailableAmount()),UserAccountBalance::getAvailableAmount, request.getAvailableAmount())
                .eq(ObjectUtil.isNotEmpty(request.getFrozenAmount()),UserAccountBalance::getFrozenAmount, request.getFrozenAmount())
                .eq(ObjectUtil.isNotEmpty(request.getCurrency()),UserAccountBalance::getCurrency, request.getCurrency());

        Page<UserAccountBalance> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, UserAccountBalancePageRes.class);
        }



    @Override
    public List<UserAccountBalancePageRes> selectBasicList(UserAccountBalanceListReq request) {
        List<UserAccountBalance> basicList = baseMapper.selectList(Wrappers.<UserAccountBalance>lambdaQuery());
        return BeanUtil.copyToList(basicList, UserAccountBalancePageRes.class);
    }

    @Override
    public UserAccountBalanceDetailRes selectByIdBasic(Long id) {
        UserAccountBalance record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, UserAccountBalanceDetailRes.class);
    }

    @Override
    public boolean saveBasic(UserAccountBalanceSaveReq record, ExtendData extendData) {
        UserAccountBalance saveRecord = BeanUtil.copyProperties(record, UserAccountBalance.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(UserAccountBalanceUpdateReq record, ExtendData extendData) {
        UserAccountBalance updateRecord = BeanUtil.copyProperties(record, UserAccountBalance.class);
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public AccountBalanceRes getAccountBalance(Long userId) {
        UserDetailRes user = userService.selectByIdBasic(userId);
        Assert.isTrue(ObjectUtil.isNotNull(user) && user.getAccountRole() == AccountTypeEnum.client , "用户不存在");
        return getAccountBalanceByCurrency(CurrencyConstants.COIN_USDT, userId);
    }

    @Override
    public AccountBalanceRes getAccountBalanceByCurrency(String currency, Long userId) {
        Assert.notNull(userId, "用户ID不能为空");

        log.info("查询用户账户余额，用户ID：{}，币种：{}", userId, currency);

        // 查询用户账户余额
        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(UserAccountBalance::getUserId, userId)
                .eq(UserAccountBalance::getCurrency, currency);

        UserAccountBalance userAccountBalance = baseMapper.selectOne(queryWrapper);

        // 如果账户不存在，返回零余额
        if (ObjectUtil.isNull(userAccountBalance)) {
            log.info("用户账户不存在，返回零余额，用户ID：{}，币种：{}", userId, currency);
            return AccountBalanceRes.builder()
                    .userId(userId)
                    .currency(currency)
                    .availableAmount(BigDecimal.ZERO)
                    .frozenAmount(BigDecimal.ZERO)
                    .totalAmount(BigDecimal.ZERO)
                    .build();
        }

        // 计算总余额
        BigDecimal totalAmount = userAccountBalance.getAvailableAmount()
                .add(userAccountBalance.getFrozenAmount());

        AccountBalanceRes result = AccountBalanceRes.builder()
                .userId(userId)
                .currency(currency)
                .availableAmount(userAccountBalance.getAvailableAmount())
                .frozenAmount(userAccountBalance.getFrozenAmount())
                .totalAmount(totalAmount)
                .build();

        log.info("查询用户账户余额成功，用户ID：{}，币种：{}，可用余额：{}，冻结余额：{}，总余额：{}",
                userId, currency, result.getAvailableAmount(), result.getFrozenAmount(), result.getTotalAmount());

        return result;
    }

}