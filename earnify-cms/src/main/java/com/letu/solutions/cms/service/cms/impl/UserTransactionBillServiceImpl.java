package com.letu.solutions.cms.service.cms.impl;

import com.letu.solutions.model.dto.UserTransactionBillDto;
import com.letu.solutions.model.entity.cms.PlatformTransactionBill;
import com.letu.solutions.model.entity.cms.UserTransactionBill;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.cms.mapper.cms.UserTransactionBillMapper;
import com.letu.solutions.cms.mapper.cms.UserAccountBalanceMapper;
import com.letu.solutions.cms.service.cms.UserTransactionBillService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.util.util.TimeUtil;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.UserTransactionBillListReq;
import com.letu.solutions.model.cms.request.cms.UserTransactionBillSaveReq;
import com.letu.solutions.model.cms.request.cms.UserTransactionBillUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserTransactionBillPageRes;
import com.letu.solutions.model.cms.response.cms.UserTransactionBillDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import java.math.BigDecimal;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.UserTransactionBillExcel;
import com.letu.solutions.cms.util.FundTypeUtil;
import java.util.stream.Collectors;

/**
 * 账户流水表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserTransactionBillServiceImpl extends ServiceImpl<UserTransactionBillMapper, UserTransactionBill> implements UserTransactionBillService {

    private final UserAccountBalanceMapper userAccountBalanceMapper;
    @Override
    public Page<UserTransactionBillPageRes> selectBasicPage(Page<UserTransactionBill> page, UserTransactionBillListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<UserTransactionBill> queryWrapper = Wrappers.<UserTransactionBill>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),UserTransactionBill::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getUserId()),UserTransactionBill::getUserId, request.getUserId())
                .eq(ObjectUtil.isNotEmpty(request.getUserName()),UserTransactionBill::getUserName, request.getUserName())
                .eq(ObjectUtil.isNotEmpty(request.getAccountType()),UserTransactionBill::getAccountType, request.getAccountType())
                .eq(ObjectUtil.isNotEmpty(request.getFundType()),UserTransactionBill::getFundType, request.getFundType())
                .ge(ObjectUtil.isNotEmpty(request.getBeginDate()), UserTransactionBill::getDay, request.getBeginDate())
                .le(ObjectUtil.isNotEmpty(request.getEndDate()), UserTransactionBill::getDay, request.getEndDate());

        // 查询数据
        Page<UserTransactionBill> basicPage = baseMapper.selectPage(page, queryWrapper);

        // 转换为响应对象
        Page<UserTransactionBillPageRes> resultPage = PageUtil.builderPage(basicPage, UserTransactionBillPageRes.class);

        // 批量查询用户余额信息，避免N+1查询问题
        List<UserTransactionBillPageRes> records = resultPage.getRecords();
        if (!records.isEmpty()) {
            // 收集所有用户ID
            List<Long> userIds = records.stream()
                    .map(UserTransactionBillPageRes::getUserId)
                    .filter(ObjectUtil::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询用户余额
            Map<Long, UserAccountBalance> userBalanceMap = getUserBalanceMap(userIds);

            // 使用lambda表达式设置冻结方向类型和当前余额
            resultPage.setRecords(records.stream()
                    .peek(record -> {
                        // 使用FundTypeUtil判断冻结方向类型
                        if (record.getFundType() != null) {
                            record.setFrozenSide(FundTypeUtil.getFrozenSideType(record.getFundType()));
                        }

                        // 设置用户当前账户余额
                        if (record.getUserId() != null) {
                            UserAccountBalance currentBalance = userBalanceMap.get(record.getUserId());
                            if (currentBalance != null) {
                                record.setCurrentAvailableAmount(currentBalance.getAvailableAmount());
                                record.setCurrentFrozenAmount(currentBalance.getFrozenAmount());
                            }
                        }
                    })
                    .collect(Collectors.toList()));
        }

        return resultPage;
    }



    @Override
    public List<UserTransactionBillPageRes> selectBasicList(UserTransactionBillListReq request) {
        List<UserTransactionBill> basicList = baseMapper.selectList(Wrappers.<UserTransactionBill>lambdaQuery());

        // 使用lambda表达式转换并设置冻结方向类型
        return basicList.stream()
                .map(record -> {
                    UserTransactionBillPageRes res = BeanUtil.copyProperties(record, UserTransactionBillPageRes.class);
                    // 使用FundTypeUtil判断冻结方向类型
                    if (record.getFundType() != null) {
                        res.setFrozenSide(FundTypeUtil.getFrozenSideType(record.getFundType()));
                    }
                    return res;
                })
                .collect(Collectors.toList());
    }

    @Override
    public UserTransactionBillDetailRes selectByIdBasic(Long id) {
        UserTransactionBill record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, UserTransactionBillDetailRes.class);
    }

    @Override
    public boolean saveBasic(UserTransactionBillSaveReq record, ExtendData extendData) {
        UserTransactionBill saveRecord = BeanUtil.copyProperties(record, UserTransactionBill.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(UserTransactionBillUpdateReq record, ExtendData extendData) {
        UserTransactionBill updateRecord = BeanUtil.copyProperties(record, UserTransactionBill.class);
        updateRecord.setUpdateTime(DateUtil.date());
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public void createUserTransactionBill(UserTransactionBillDto dto, ExtendData extendData) {
        UserTransactionBillSaveReq req = UserTransactionBillSaveReq.builder()
                .userId(dto.getUserId())
                .userName(dto.getUserName())
                .accountType(dto.getAccountType())
                .fundType(dto.getFundType())
                .side(dto.getSide())
                .amount(dto.getAmount())
                .currency(dto.getCurrency())
                .balanceBefore(dto.getBefore())
                .balanceAfter(dto.getAfter())
                .frozen(dto.getFrozen() != null ? dto.getFrozen() : BigDecimal.ZERO)
                .remark(dto.getRemark())
                .day(TimeUtil.today(new Date()))
                .build();
        this.saveBasic(req, extendData);
    }

    @Override
    public String exportExcel(com.letu.solutions.model.cms.request.cms.UserTransactionBillListReq request) {
        // 查询数据
        List<UserTransactionBillPageRes> data = selectBasicList(request);

        // 使用lambda表达式转为Excel DTO
        List<UserTransactionBillExcel> excelList = data.stream()
                .map(record -> BeanUtil.copyProperties(record, UserTransactionBillExcel.class))
                .collect(Collectors.toList());

        return ExcelUtil.getExcel("账户流水导出", UserTransactionBillExcel.class, excelList, false);
    }

    /**
     * 批量查询用户账户余额，返回Map便于快速查找
     * @param userIds 用户ID列表
     * @return 用户ID -> 账户余额的映射
     */
    private Map<Long, UserAccountBalance> getUserBalanceMap(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Map.of();
        }

        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .in(UserAccountBalance::getUserId, userIds)
                .eq(UserAccountBalance::getCurrency, "USDT"); // 默认查询USDT余额

        List<UserAccountBalance> balances = userAccountBalanceMapper.selectList(queryWrapper);

        // 转换为Map，便于快速查找
        return balances.stream()
                .collect(Collectors.toMap(UserAccountBalance::getUserId, Function.identity()));
    }

    /**
     * 查询单个用户当前账户余额（保留此方法用于其他地方调用）
     * @param userId 用户ID
     * @return 用户账户余额信息
     */
    private UserAccountBalance getCurrentUserBalance(Long userId) {
        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(UserAccountBalance::getUserId, userId)
                .eq(UserAccountBalance::getCurrency, "USDT"); // 默认查询USDT余额
        return userAccountBalanceMapper.selectOne(queryWrapper);
    }
}