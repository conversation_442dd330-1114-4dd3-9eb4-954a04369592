package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.WithdrawRecordService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordListReq;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordSaveReq;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordUpdateReq;
import com.letu.solutions.model.cms.response.cms.WithdrawRecordPageRes;
import com.letu.solutions.model.cms.response.cms.WithdrawRecordDetailRes;

/**
 * 财务管理/用户提现管理
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:withdraw_record", valueDesc = "用户:提现记录表")
public class WithdrawRecordController{
    private final WithdrawRecordService basicService;
    /**
    * 提现记录表 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/withdrawRecord/page")
    public R<Page<WithdrawRecordPageRes>> loadPage( WithdrawRecordListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 提现记录表 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/withdrawRecord/list")
    public R<List<WithdrawRecordPageRes>> loadList(WithdrawRecordListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 提现记录 Excel导出
     */
    @Preauthorize(value = "list", valueDesc = "提现记录导出")
    @GetMapping("/withdrawRecord/exportExcel")
    public R<String> exportExcel(WithdrawRecordListReq request) {
        return R.success(basicService.exportExcel(request));
    }

    /**
     * 提现记录表 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/withdrawRecord/selectById")
    public R<WithdrawRecordDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/withdrawRecord/insert")
    public R<Boolean> insert(@Validated @RequestBody WithdrawRecordSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 提现记录 处理
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/withdrawRecord/update")
    public R<Boolean> update(@Validated @RequestBody WithdrawRecordUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

}
