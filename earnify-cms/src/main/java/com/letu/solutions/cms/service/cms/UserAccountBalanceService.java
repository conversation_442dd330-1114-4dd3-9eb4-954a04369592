package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.customer.response.AccountBalanceRes;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceListReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceSaveReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserAccountBalancePageRes;
import com.letu.solutions.model.cms.response.cms.UserAccountBalanceDetailRes;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 账户资金信息 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface UserAccountBalanceService extends IService<com.letu.solutions.model.entity.cms.UserAccountBalance> {
    Page<UserAccountBalancePageRes> selectBasicPage(Page<UserAccountBalance> page, UserAccountBalanceListReq request);

    List<UserAccountBalancePageRes> selectBasicList(UserAccountBalanceListReq request);

    UserAccountBalanceDetailRes selectByIdBasic(Long id);

    boolean saveBasic(UserAccountBalanceSaveReq record, ExtendData extendData);

    boolean updateBasic(UserAccountBalanceUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    /**
     * 获取用户账户余额信息（默认USDT）
     *
     * @return 账户余额信息
     */
    AccountBalanceRes getAccountBalance(Long userId);

    /**
     * 获取用户账户余额信息（指定币种）
     *
     * @param currency 币种
     * @return 账户余额信息
     */
    AccountBalanceRes getAccountBalanceByCurrency(String currency, Long userId);
}
