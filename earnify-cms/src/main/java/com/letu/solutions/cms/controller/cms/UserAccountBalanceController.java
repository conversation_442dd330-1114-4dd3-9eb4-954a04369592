package com.letu.solutions.cms.controller.cms;

import com.letu.solutions.model.customer.response.AccountBalanceRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.UserAccountBalanceService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceListReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceSaveReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserAccountBalancePageRes;
import com.letu.solutions.model.cms.response.cms.UserAccountBalanceDetailRes;

/**
 * 用户/甲方账户资金信息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:user_account_balance", valueDesc = "用户:账户资金信息")
public class UserAccountBalanceController{
    private final UserAccountBalanceService accountService;

    /**
     * 获取甲方用户余额
     */
    @GetMapping("/account/balance")
    public R<AccountBalanceRes> getBalance(@RequestParam Long userId) {
        return R.success(accountService.getAccountBalance(userId));
    }
}
