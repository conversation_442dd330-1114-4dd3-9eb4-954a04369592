package com.letu.solutions.cms.service.sys;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.sys.SysParam;
import java.util.List;
import com.letu.solutions.model.cms.request.sys.SysParamListReq;
import com.letu.solutions.model.cms.request.sys.SysParamSaveReq;
import com.letu.solutions.model.cms.request.sys.SysParamUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysParamPageRes;
import com.letu.solutions.model.cms.response.sys.SysParamDetailRes;

/**
 * 系统参数 服务类
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
public interface SysParamService {
    Page<SysParamPageRes> selectBasicPage(Page<SysParam> page, SysParamListReq request);

    List<SysParamPageRes> selectBasicList(SysParamListReq request);

    SysParamDetailRes selectByIdBasic(Long id);

    boolean saveBasic(SysParamSaveReq record, ExtendData extendData);

    boolean updateBasic(SysParamUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    /**
     * 根据key获取整数值
     * 
     * @param key 参数key
     * @param defaultValue 默认值
     * @return 整数值
     */
    Integer getIntValueByKey(String key, Integer defaultValue);
}
