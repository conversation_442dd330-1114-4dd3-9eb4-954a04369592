package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.AppealRecordExcel;
import com.letu.solutions.cms.dto.TaskExcel;
import com.letu.solutions.cms.mapper.cms.PlatformTransactionBillMapper;
import com.letu.solutions.cms.mapper.cms.TaskMapper;
import com.letu.solutions.cms.mapper.cms.UserTaskMapper;
import com.letu.solutions.cms.service.user.UserService;
import com.letu.solutions.core.configuration.AccountConfiguration;
import com.letu.solutions.model.cms.request.cms.*;
import com.letu.solutions.model.cms.response.user.UserDetailRes;
import com.letu.solutions.model.entity.cms.*;
import com.letu.solutions.cms.mapper.cms.AppealRecordMapper;
import com.letu.solutions.cms.service.cms.AppealRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;
import java.util.Date;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.response.cms.AppealRecordPageRes;
import com.letu.solutions.model.cms.response.cms.AppealRecordDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import com.letu.solutions.cms.service.cms.UserAccountBalanceService;
import com.letu.solutions.cms.service.cms.PlatformTransactionBillService;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.cms.service.cms.TaskService;

import java.math.BigDecimal;

/**
 * 申述记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AppealRecordServiceImpl extends ServiceImpl<AppealRecordMapper, AppealRecord> implements AppealRecordService {
    private final UserAccountBalanceService userAccountBalanceService;
    private final PlatformTransactionBillMapper platformTransactionBillMapper;
    private final TransactionalManage transactionalManage;
    private final TaskMapper taskMapper;
    private final UserTaskMapper userTaskMapper;
    private final AccountConfiguration accountConfiguration;
    private final UserService userService;

    @Override
    public Page<AppealRecordPageRes> selectBasicPage(Page<AppealRecord> page, AppealRecordListReq request) {
        // 调用自定义SQL实现多表联合分页
        return baseMapper.selectBasicPage(page, request);
    }


    @Override
    public String selectBasicList(AppealRecordListReq request) {
        List<AppealRecordExcel> basicList = baseMapper.selectBasicList(request);
        return ExcelUtil.getExcel("申述记录列表", AppealRecordExcel.class, basicList, false);
    }

    @Override
    public AppealRecordDetailRes selectByIdBasic(Long id) {
        AppealRecord record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, AppealRecordDetailRes.class);
    }

    @Override
    public boolean updateBasic(AppealRecordUpdateReq record, ExtendData extendData) {
        AppealRecord updateRecord = BeanUtil.copyProperties(record, AppealRecord.class);
        AppealRecord appeal = baseMapper.selectById(record.getId());
        Long partyAId = appeal.getPartyAUserId();
        Long partyBId = appeal.getPartyBUserId();
        // 查询任务获取price
        Task task = taskMapper.selectById(appeal.getTaskId());
        UserTask userTask = userTaskMapper.selectOne(Wrappers.<UserTask>lambdaQuery()
                .eq(UserTask::getTaskId, task.getId())
                .eq(UserTask::getUserId, appeal.getPartyBUserId())
                .eq(UserTask::getState, UserTaskStatusEnum.appealInProgress));
        Assert.notNull(userTask,"用户当前任务没有申诉中");
        BigDecimal amount = task.getPrice();
        // 甲方余额
        UserAccountBalance aBalance = userAccountBalanceService.getOne(Wrappers.<UserAccountBalance>lambdaQuery().eq(UserAccountBalance::getUserId, partyAId));
        Assert.notNull(aBalance, "甲方账户余额记录不存在");
        
        // 乙方余额
        UserAccountBalance bBalance = userAccountBalanceService.getOne(Wrappers.<UserAccountBalance>lambdaQuery().eq(UserAccountBalance::getUserId, partyBId));
        UserDetailRes auser = userService.selectByIdBasic(partyAId);
        UserDetailRes buser = userService.selectByIdBasic(partyBId);
        Boolean result = transactionalManage.execute(() -> {
            // 如果乙方没有账户余额记录，创建一个新的记录
            if (bBalance == null) {
                UserAccountBalance newBalance = new UserAccountBalance();
                newBalance.setUserId(partyBId);
                newBalance.setAvailableAmount(BigDecimal.ZERO);
                newBalance.setFrozenAmount(BigDecimal.ZERO);
                newBalance.setCreateTime(new Date());
                newBalance.setUpdateTime(new Date());
                userAccountBalanceService.save(newBalance);
                // 重新查询获取创建的记录
                UserAccountBalance createdBalance = userAccountBalanceService.getOne(Wrappers.<UserAccountBalance>lambdaQuery().eq(UserAccountBalance::getUserId, partyBId));
                // 在 lambda 内部使用 createdBalance 替代 bBalance
                return executeAppealLogic(updateRecord, appeal, task, userTask, amount, partyAId, partyBId, aBalance, createdBalance, auser, buser, record);
            } else {
                return executeAppealLogic(updateRecord, appeal, task, userTask, amount, partyAId, partyBId, aBalance, bBalance, auser, buser, record);
            }

        });
        Assert.isTrue(result, "申述审核失败");
        return result;
    }
    
    /**
     * 执行申诉逻辑
     */
    private Boolean executeAppealLogic(AppealRecord updateRecord, AppealRecord appeal, Task task, UserTask userTask, 
                                     BigDecimal amount, Long partyAId, Long partyBId, UserAccountBalance aBalance, 
                                     UserAccountBalance bBalance, UserDetailRes auser, UserDetailRes buser, 
                                     AppealRecordUpdateReq record) {
        Boolean un = baseMapper.updateById(updateRecord) > 0;
        if (record.getAppealStatus() != null && record.getAppealStatus() == 1) {
            // 甲方冻结金额减少
            un = userAccountBalanceService.update(Wrappers.<UserAccountBalance>lambdaUpdate()
                    .eq(UserAccountBalance::getUserId, partyAId)
                    .eq(UserAccountBalance::getFrozenAmount, aBalance.getFrozenAmount())
                    .setSql("frozen_amount = frozen_amount - " + amount));
            // 甲方冻结金额减少流水
            PlatformTransactionBill freezeBill = new PlatformTransactionBill();
            freezeBill.setUserId(partyAId);
            freezeBill.setAccountType(AccountTypeEnum.client); // 甲方
            freezeBill.setFundType(FundTypeEnum.task_reward_unfreeze_send); // 提现/扣除
            freezeBill.setPlatformUserId(accountConfiguration.getPlatformUserId());
            freezeBill.setUserName(auser.getNickName());
            freezeBill.setSide(FundSideTypeEnum.out); // 出账
            freezeBill.setAmount(amount);
            freezeBill.setCurrency("USDT");
            freezeBill.setFee(BigDecimal.ZERO);
            freezeBill.setBalanceBefore(aBalance.getFrozenAmount().add(amount));
            freezeBill.setBalanceAfter(aBalance.getFrozenAmount());
            freezeBill.setFundId(appeal.getId());
            freezeBill.setRemark("申诉通过，甲方冻结金额减少");
            un = platformTransactionBillMapper.insert(freezeBill) > 0;
            // 乙方余额增加
            un = userAccountBalanceService.update(Wrappers.<UserAccountBalance>lambdaUpdate()
                    .eq(UserAccountBalance::getUserId, partyBId)
                    .eq(UserAccountBalance::getAvailableAmount, bBalance.getAvailableAmount())
                    .setSql("available_amount = available_amount + " + amount));
            // 乙方流水
            PlatformTransactionBill bill = new PlatformTransactionBill();
            bill.setUserId(partyBId);
            bill.setAccountType(AccountTypeEnum.provider); // 乙方
            bill.setFundType(FundTypeEnum.task_reward_to_account);
            bill.setPlatformUserId(accountConfiguration.getPlatformUserId());
            bill.setUserName(buser.getNickName());
            bill.setSide(FundSideTypeEnum.in); // 入账
            bill.setAmount(amount);
            bill.setCurrency("USDT"); // 如有币种字段可替换
            bill.setFee(BigDecimal.ZERO);
            bill.setBalanceBefore(bBalance.getAvailableAmount().subtract(amount));
            bill.setBalanceAfter(bBalance.getAvailableAmount());
            bill.setFundId(appeal.getId());
            bill.setRemark("申诉通过，乙方获得奖励");
            un = platformTransactionBillMapper.insert(bill) > 0;
            un = taskMapper.update(Wrappers.<Task>lambdaUpdate().
                    set(Task::getTaskFinishNum, task.getTaskFinishNum() + 1)
                    .eq(Task::getId, task.getId())
                    .eq(Task::getTaskFinishNum, task.getTaskFinishNum())) > 0;
            userTask.setState(UserTaskStatusEnum.appealPass);
            un = userTaskMapper.updateById(userTask) > 0;
        } else if (record.getAppealStatus() != null && record.getAppealStatus() == 2) {
            // 甲方冻结金额减少
            un = userAccountBalanceService.update(Wrappers.<UserAccountBalance>lambdaUpdate()
                    .eq(UserAccountBalance::getUserId, partyAId)
                    .eq(UserAccountBalance::getFrozenAmount, aBalance.getFrozenAmount())
                    .setSql("frozen_amount = frozen_amount - " + amount));
            // 甲方冻结金额减少流水
            PlatformTransactionBill freezeBill = new PlatformTransactionBill();
            freezeBill.setUserId(partyAId);
            freezeBill.setAccountType(AccountTypeEnum.client); // 甲方
            freezeBill.setFundType(FundTypeEnum.task_reward_unfreeze_send); // 提现/扣除
            freezeBill.setPlatformUserId(accountConfiguration.getPlatformUserId());
            freezeBill.setUserName(auser.getNickName());
            freezeBill.setSide(FundSideTypeEnum.out); // 出账
            freezeBill.setAmount(amount);
            freezeBill.setCurrency("USDT");
            freezeBill.setFee(BigDecimal.ZERO);
            freezeBill.setBalanceBefore(aBalance.getFrozenAmount().add(amount));
            freezeBill.setBalanceAfter(aBalance.getFrozenAmount());
            freezeBill.setFundId(appeal.getId());
            freezeBill.setRemark("申诉不通过，甲方冻结金额减少");
            un = platformTransactionBillMapper.insert(freezeBill) > 0;
            // 甲方余额增加
            un = userAccountBalanceService.update(Wrappers.<UserAccountBalance>lambdaUpdate()
                    .eq(UserAccountBalance::getUserId, partyAId)
                    .eq(UserAccountBalance::getAvailableAmount, aBalance.getAvailableAmount())
                    .setSql("available_amount = available_amount + " + amount));
            // 甲方余额入账流水
            PlatformTransactionBill bill = new PlatformTransactionBill();
            bill.setUserId(partyAId);
            bill.setAccountType(AccountTypeEnum.client); // 甲方
            bill.setFundType(FundTypeEnum.task_reward_unfreeze_refund);
            bill.setPlatformUserId(accountConfiguration.getPlatformUserId());
            bill.setUserName(auser.getNickName());
            bill.setSide(FundSideTypeEnum.in); // 入账
            bill.setAmount(amount);
            bill.setCurrency("USDT");
            bill.setFee(BigDecimal.ZERO);
            bill.setBalanceBefore(aBalance.getAvailableAmount().subtract(amount));
            bill.setBalanceAfter(aBalance.getAvailableAmount());
            bill.setFundId(appeal.getId());
            bill.setRemark("申诉不通过，甲方返还冻结金额");
            un = platformTransactionBillMapper.insert(bill) > 0;
            userTask.setState(UserTaskStatusEnum.appealNotPass);
            un = userTaskMapper.updateById(userTask) > 0;
        }
        return un;
    }

}