<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>earnify-async</artifactId>
    <version>0.0.2-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>earnify-async</name>
    <description>异步处理项目</description>

    <parent>
        <groupId>com.letu.solutions</groupId>
        <artifactId>novel</artifactId>
        <version>0.0.2-SNAPSHOT</version>
    </parent>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--服务基础模块-->
        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-core</artifactId>
            <version>0.0.2-SNAPSHOT</version>
            <exclusions>
                <!-- 排除当前服务不需要的es-->
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-feign</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <!--公共模块-->
        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-model</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-util</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>

                    <mainClass>com.letu.solutions.message.AsyncApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>

        <!-- 定义包含这些资源文件，能在jar包中获取这些文件 -->
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.cert</include>
                </includes>
                <!--是否替换资源中的属性-->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
</project>
