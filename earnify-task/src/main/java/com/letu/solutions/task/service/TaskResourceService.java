package com.letu.solutions.task.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.letu.solutions.core.enums.NotificationTemplateEnum;
import com.letu.solutions.dubbo.earnify.customer.MessageLetterFacade;
import com.letu.solutions.model.entity.cms.Task;
import com.letu.solutions.model.entity.cms.UserTask;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import com.letu.solutions.task.mapper.TaskMapper;
import com.letu.solutions.task.mapper.UserTaskMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 任务资源管理服务
 * 提供任务资源的回收、分配等公共功能
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskResourceService {

    private final TaskMapper taskMapper;
    private final UserTaskMapper userTaskMapper;
    
    @DubboReference(check = false)
    private MessageLetterFacade messageLetterFacade;

    /**
     * 批量标记任务为失败并回收资源
     * 
     * @param timeoutTasks 超时的用户任务列表
     * @param failureReason 失败原因
     * @return 成功处理的任务数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int markTasksAsFailedAndRecycleResources(List<UserTask> timeoutTasks, String failureReason) {
        int successCount = 0;
        
        for (UserTask userTask : timeoutTasks) {
            try {
                if (markSingleTaskAsFailed(userTask, failureReason)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("处理用户任务{}失败时发生异常", userTask.getId(), e);
            }
        }
        
        log.info("批量处理任务失败完成，成功处理{}个任务，失败原因：{}", successCount, failureReason);
        return successCount;
    }

    /**
     * 标记单个任务为失败并回收资源
     * 
     * @param userTask 用户任务
     * @param failureReason 失败原因
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean markSingleTaskAsFailed(UserTask userTask, String failureReason) {
        try {
            // 1. 检查任务是否已经被处理过（避免重复处理）
            UserTask currentUserTask = userTaskMapper.selectById(userTask.getId());
            if (currentUserTask == null || currentUserTask.getState() == UserTaskStatusEnum.systemFailure) {
                log.info("用户任务{}已经被处理过或不存在，跳过处理", userTask.getId());
                return false;
            }
            
            // 2. 更新用户任务状态为系统判定失败
            boolean updateResult = userTaskMapper.update(null,
                    new LambdaUpdateWrapper<UserTask>()
                            .set(UserTask::getState, UserTaskStatusEnum.systemFailure)
                            .set(UserTask::getUpdateTime, new Date())
                            .eq(UserTask::getId, userTask.getId())
                            .eq(UserTask::getState, userTask.getState()) // 乐观锁：确保状态未变
            ) > 0;
            
            if (updateResult) {
                log.info("用户任务{}状态已更新为系统判定失败，原因：{}", userTask.getId(), failureReason);
                
                // 3. 回收任务资源
                recycleTaskResource(userTask.getTaskId());
                
                // 4. 发送任务失败站内信
                try {
                    messageLetterFacade.sendMessageLetter(userTask.getUserId(), NotificationTemplateEnum.TASK_FAILED);
                    log.info("已为用户{}发送任务失败站内信，任务ID：{}", userTask.getUserId(), userTask.getId());
                } catch (Exception e) {
                    log.error("发送任务失败站内信失败，用户ID：{}，任务ID：{}", userTask.getUserId(), userTask.getId(), e);
                }
                
                return true;
            } else {
                log.warn("更新用户任务{}状态失败，可能已被其他进程处理", userTask.getId());
                return false;
            }
        } catch (Exception e) {
            log.error("处理用户任务{}失败时发生异常", userTask.getId(), e);
            return false;
        }
    }

    /**
     * 回收任务资源（减少已领取数量，使任务重新可用）
     * 
     * @param taskId 任务ID
     * @return 是否回收成功
     */
    public boolean recycleTaskResource(Long taskId) {
        try {
            // 使用原子性更新，减少task_receive_num
            int updated = taskMapper.update(null,
                    new LambdaUpdateWrapper<Task>()
                            .setSql("task_receive_num = task_receive_num - 1")
                            .eq(Task::getId, taskId)
                            .gt(Task::getTaskReceiveNum, 0) // 确保不会减到负数
            );
            
            if (updated > 0) {
                log.info("任务{}资源回收成功，已减少领取数量，任务重新可用", taskId);
                return true;
            } else {
                log.warn("任务{}资源回收失败，可能领取数量已为0", taskId);
                return false;
            }
        } catch (Exception e) {
            log.error("回收任务{}资源时发生异常", taskId, e);
            return false;
        }
    }

    /**
     * 分配任务资源（增加已领取数量）
     * 
     * @param taskId 任务ID
     * @return 是否分配成功
     */
    public boolean allocateTaskResource(Long taskId) {
        try {
            // 使用原子性更新，增加task_receive_num
            int updated = taskMapper.update(null,
                    new LambdaUpdateWrapper<Task>()
                            .setSql("task_receive_num = task_receive_num + 1")
                            .eq(Task::getId, taskId)
                            .apply("task_receive_num < number") // 确保不超过总数量
            );
            
            if (updated > 0) {
                log.info("任务{}资源分配成功，已增加领取数量", taskId);
                return true;
            } else {
                log.warn("任务{}资源分配失败，可能任务已满", taskId);
                return false;
            }
        } catch (Exception e) {
            log.error("分配任务{}资源时发生异常", taskId, e);
            return false;
        }
    }

    /**
     * 完成任务资源（增加已完成数量）
     * 
     * @param taskId 任务ID
     * @return 是否完成成功
     */
    public boolean completeTaskResource(Long taskId) {
        try {
            // 使用原子性更新，增加task_finish_num
            int updated = taskMapper.update(null,
                    new LambdaUpdateWrapper<Task>()
                            .setSql("task_finish_num = task_finish_num + 1")
                            .eq(Task::getId, taskId)
                            .apply("task_finish_num <= task_receive_num") // 确保已完成数不超过已领取数
            );
            
            if (updated > 0) {
                log.info("任务{}完成成功，已增加完成数量", taskId);
                return true;
            } else {
                log.warn("任务{}完成失败，可能已完成数已等于领取数", taskId);
                return false;
            }
        } catch (Exception e) {
            log.error("完成任务{}时发生异常", taskId, e);
            return false;
        }
    }
    /**
     * 任务资源信息
     */
    public static class TaskResourceInfo {
        private Long taskId;
        private Integer totalNumber;      // 总数量
        private Integer receivedNumber;   // 已领取数量
        private Integer finishedNumber;   // 已完成数量
        private Integer availableNumber;  // 可用数量

        // 使用Builder模式
        public static TaskResourceInfoBuilder builder() {
            return new TaskResourceInfoBuilder();
        }

        public static class TaskResourceInfoBuilder {
            private TaskResourceInfo info = new TaskResourceInfo();

            public TaskResourceInfoBuilder taskId(Long taskId) {
                info.taskId = taskId;
                return this;
            }

            public TaskResourceInfoBuilder totalNumber(Integer totalNumber) {
                info.totalNumber = totalNumber;
                return this;
            }

            public TaskResourceInfoBuilder receivedNumber(Integer receivedNumber) {
                info.receivedNumber = receivedNumber;
                return this;
            }

            public TaskResourceInfoBuilder finishedNumber(Integer finishedNumber) {
                info.finishedNumber = finishedNumber;
                return this;
            }

            public TaskResourceInfoBuilder availableNumber(Integer availableNumber) {
                info.availableNumber = availableNumber;
                return this;
            }

            public TaskResourceInfo build() {
                return info;
            }
        }

        // Getters
        public Long getTaskId() { return taskId; }
        public Integer getTotalNumber() { return totalNumber; }
        public Integer getReceivedNumber() { return receivedNumber; }
        public Integer getFinishedNumber() { return finishedNumber; }
        public Integer getAvailableNumber() { return availableNumber; }
    }
} 