package com.letu.solutions.task.handle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import cn.hutool.core.date.DateUtil;
import com.letu.solutions.model.entity.cms.UserTask;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import com.letu.solutions.task.mapper.UserTaskMapper;
import com.letu.solutions.task.service.TaskResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 任务超时处理定时任务
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskTimeoutHandle {

    private final UserTaskMapper userTaskMapper;
    private final TaskResourceService taskResourceService;

    /**
     * 检查任务超时并自动标记为失败
     * 执行频率：每小时执行一次
     * 
     * 超时检查逻辑：
     * 1. 任务到期超时：检查进行中的任务是否超过了任务到期时间(time字段)
     * 2. 审核超时：检查待审核的任务是否超过了审核到期时间(examineTime字段)
     * 3. 申诉处理超时：检查甲方驳回的任务是否超过了申诉处理到期时间(rejectTime字段)
     * 4. 申诉有效期超时：检查申诉中的任务是否超过了申诉有效期(appealTime字段)
     */
    @XxlJob("checkTaskTimeoutAndMarkFailure")
    public ReturnT<String> checkTaskTimeoutAndMarkFailure() {
        log.info("开始执行任务超时检查定时任务");
        
        try {
            // 1. 检查任务到期超时
            checkTaskExpireTimeout();
            
            // 2. 检查审核超时
            checkAuditTimeout();
            
            // 3. 检查申诉处理超时
            checkAppealProcessTimeout();
            
            // 4. 检查申诉有效期超时
            checkAppealValidTimeout();
            
            log.info("任务超时检查定时任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("任务超时检查定时任务执行失败", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 检查任务到期超时
     * 检查进行中的任务是否超过了任务到期时间
     */
    private void checkTaskExpireTimeout() {
        Date currentDate = new Date();
        
        List<UserTask> timeoutTasks = userTaskMapper.selectList(
                new LambdaQueryWrapper<UserTask>()
                        .eq(UserTask::getState, UserTaskStatusEnum.incomplete)
                        .lt(UserTask::getTime, currentDate)
        );
        
        if (!timeoutTasks.isEmpty()) {
            log.info("发现{}个任务到期超时的用户任务", timeoutTasks.size());
            markTasksAsFailed(timeoutTasks, "任务到期超时");
        }
    }

    /**
     * 检查审核超时
     * 检查待审核的任务是否超过了审核到期时间
     */
    private void checkAuditTimeout() {
        Date currentDate = new Date();
        
        List<UserTask> timeoutTasks = userTaskMapper.selectList(
                new LambdaQueryWrapper<UserTask>()
                        .eq(UserTask::getState, UserTaskStatusEnum.pendingApproval)
                        .lt(UserTask::getExamineTime, currentDate)
        );
        
        if (!timeoutTasks.isEmpty()) {
            log.info("发现{}个审核超时的用户任务", timeoutTasks.size());
            markTasksAsFailed(timeoutTasks, "审核超时");
        }
    }

    /**
     * 检查申诉处理超时
     * 检查甲方驳回的任务是否超过了申诉处理到期时间
     */
    private void checkAppealProcessTimeout() {
        Date currentDate = new Date();
        
        List<UserTask> timeoutTasks = userTaskMapper.selectList(
                new LambdaQueryWrapper<UserTask>()
                        .eq(UserTask::getState, UserTaskStatusEnum.partyARejects)
                        .lt(UserTask::getRejectTime, currentDate)
        );
        
        if (!timeoutTasks.isEmpty()) {
            log.info("发现{}个申诉处理超时的用户任务", timeoutTasks.size());
            markTasksAsFailed(timeoutTasks, "申诉处理超时");
        }
    }

    /**
     * 检查申诉有效期超时
     * 检查申诉中的任务是否超过了申诉有效期
     */
    private void checkAppealValidTimeout() {
        Date currentDate = new Date();
        
        List<UserTask> timeoutTasks = userTaskMapper.selectList(
                new LambdaQueryWrapper<UserTask>()
                        .eq(UserTask::getState, UserTaskStatusEnum.appealInProgress)
                        .lt(UserTask::getAppealTime, currentDate)
        );
        
        if (!timeoutTasks.isEmpty()) {
            log.info("发现{}个申诉有效期超时的用户任务", timeoutTasks.size());
            markTasksAsFailed(timeoutTasks, "申诉有效期超时");
        }
    }

    /**
     * 标记任务为失败并返还任务名额
     */
    private void markTasksAsFailed(List<UserTask> timeoutTasks, String failureReason) {
        // 使用公共服务批量处理
        int successCount = taskResourceService.markTasksAsFailedAndRecycleResources(timeoutTasks, failureReason);
        log.info("批量处理任务失败完成，成功处理{}个任务，失败原因：{}", successCount, failureReason);
    }
} 