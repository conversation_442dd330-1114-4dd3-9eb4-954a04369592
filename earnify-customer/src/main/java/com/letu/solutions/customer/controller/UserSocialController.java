package com.letu.solutions.customer.controller;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.UserSocialService;
import com.letu.solutions.model.request.user.UserSocialBindRequest;
import com.letu.solutions.model.response.user.UserSocialResponse;
import com.letu.solutions.model.enums.cms.SocialTypeEnum;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.utils.LanguageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户社交媒体绑定
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class UserSocialController {

    private final UserSocialService userSocialService;

    /**
     * 绑定社交媒体
     */
    @PostMapping("/user/bindSocial")
    public R<Boolean> bindSocial(@Validated @RequestBody UserSocialBindRequest request, @RequestHeader ExtendData extendData) {
        log.info("用户{}请求绑定社交媒体，类型：{}", extendData.getUserId(), request.getSocialType().getDesc());
        
        String errorMessage = userSocialService.bindSocial(request, extendData);
        if (errorMessage == null) {
            return R.success(true);
        } else {
            return R.fail(errorMessage);
        }
    }

    /**
     * 查询用户社交媒体绑定信息
     */
    @GetMapping("/user/getSocial")
    public R<UserSocialResponse> getUserSocial(@RequestHeader ExtendData extendData) {
        log.info("用户{}请求查询社交媒体绑定信息", extendData.getUserId());
        
        UserSocialResponse response = userSocialService.getUserSocial(extendData);
        return R.success(response);
    }

//    /**
//     * 验证社交媒体绑定状态
//     */
//    @PostMapping("/user/verifySocial")
//    public R<Boolean> verifySocial(@RequestParam SocialTypeEnum socialType, @RequestHeader ExtendData extendData) {
//        log.info("用户{}请求验证社交媒体{}绑定状态", extendData.getUserId(), socialType.getDesc());
//
//        boolean result = userSocialService.verifySocial(socialType, extendData);
//        if (result) {
//            return R.success(true);
//        } else {
//            return R.fail("验证失败，请检查链接是否正确");
//        }
//    }
} 