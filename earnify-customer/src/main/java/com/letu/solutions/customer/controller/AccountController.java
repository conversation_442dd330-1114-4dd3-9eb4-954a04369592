package com.letu.solutions.customer.controller;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.AccountService;
import com.letu.solutions.model.customer.response.AccountBalanceRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户相关/用户账户相关
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/customer")
@RequiredArgsConstructor
@Validated
public class AccountController {

    private final AccountService accountService;

    /**
     * 获取用户余额信息
     */
    @GetMapping("/account/balance")
    public R<AccountBalanceRes> getBalance(@RequestHeader ExtendData extendData) {
        return R.success(accountService.getAccountBalance(extendData));
    }

//    /**
//     * 获取用户余额信息（指定币种）
//     */
//    @GetMapping("/account/balanceByCurrency")
//    public R<AccountBalanceRes> getBalanceByCurrency(@RequestParam String currency, @RequestHeader ExtendData extendData) {
//        return R.success(accountService.getAccountBalanceByCurrency(currency, extendData));
//    }
}
