package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.customer.service.FinanceConfigService;
import com.letu.solutions.customer.mapper.PlatformWalletAddressMapper;
import com.letu.solutions.customer.mapper.FinanceConfigMapper;
import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;
import com.letu.solutions.model.dto.FinanceConfigCacheDTO;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import com.letu.solutions.util.util.FinanceConfigCacheUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 财务配置服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceConfigServiceImpl implements FinanceConfigService {

    private final PlatformWalletAddressMapper platformWalletAddressMapper;
    private final FinanceConfigMapper financeConfigMapper;
    private final FinanceConfigCacheUtil financeConfigCacheUtil;

    @Override
    public FinanceConfigDetailRes selectByNetwork(String network) {
        log.info("根据协议网络查询财务配置详情，network：{}", network);

        // 先从缓存中获取
        FinanceConfigCacheDTO cacheDTO = financeConfigCacheUtil.getConfig(network);
        if (cacheDTO != null) {
            FinanceConfigDetailRes res = BeanUtil.copyProperties(cacheDTO.getConfig(), FinanceConfigDetailRes.class);
            res.setAddressList(cacheDTO.getAddressList());
            log.info("从缓存中获取财务配置详情，network：{}", network);
            return res;
        }

        // 缓存未命中，查询数据库
        log.info("缓存未命中，从数据库查询财务配置详情，network：{}", network);

        // 查询财务配置
        LambdaQueryWrapper<FinanceConfig> query = Wrappers.<FinanceConfig>lambdaQuery()
                .eq(FinanceConfig::getNetwork, network);
        FinanceConfig record = financeConfigMapper.selectOne(query);

        if (record == null) {
            log.warn("未找到协议网络的财务配置，network：{}", network);
            return null;
        }
        // 转换为响应对象
        FinanceConfigDetailRes res = BeanUtil.copyProperties(record, FinanceConfigDetailRes.class);
        // 查询钱包地址列表
        List<String> addressList = getAddressesByNetwork(network);
        res.setAddressList(addressList);

        return res;
    }

    @Override
    public List<String> getAddressesByNetwork(String network) {
        log.info("查询协议网络钱包地址列表，network：{}", network);
        return platformWalletAddressMapper.getAddressesByNetwork(network);
    }
}
