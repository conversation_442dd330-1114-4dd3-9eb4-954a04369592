package com.letu.solutions.customer.service;


import com.letu.solutions.core.model.Authentication;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.customer.model.GoogleUserInfo;
import com.letu.solutions.model.request.login.EmailLoginRequest;
import com.letu.solutions.model.request.login.GoogleLoginRequest;
import com.letu.solutions.model.request.login.RegisterRequest;
import com.letu.solutions.model.request.user.PwdUpdateReq;
import com.letu.solutions.model.request.user.UserUpdateHeadReq;
import com.letu.solutions.model.request.user.UserUpdateInfoReq;
import com.letu.solutions.model.request.user.UserUpdateNameReq;
import com.letu.solutions.model.response.login.LoginRes;
import com.letu.solutions.model.response.login.UserResponse;
import com.letu.solutions.model.response.upload.UploadResponse;
import com.letu.solutions.model.response.user.UserUpdateResult;
import com.letu.solutions.share.model.enums.ClientEnum;
import com.letu.solutions.share.model.request.user.LoginDeviceReq;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户 服务类
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface UserService {

    Long createUser(RegisterRequest request, ExtendData extendData);


    void loginOut(ExtendData extendData);

    UserResponse baseUserInfo(Long userId);

    UserResponse getUserBaseUser(Long userId);


    boolean updateName(UserUpdateNameReq req, ExtendData extendData);

    boolean updateHead(UserUpdateHeadReq req, ExtendData extendData);

    Authentication loadAuthentication(Long userId, String openId, UserResponse userResponse, ExtendData extendData);

    /**
     * 更新用户基础信息
     * Update user base information
     *
     * @param body 请求参数
     * @param extendData 扩展数据
     * @return 更新结果，包含成功状态和错误信息
     */
    UserUpdateResult updateBase(UserUpdateInfoReq body, ExtendData extendData);

    /**
     * 获取或生成用户邀请码
     * Get or generate user invite code
     *
     * @param userId 用户ID
     * @return 邀请码
     */
    String getInviteCode(Long userId);

    void updateLastLoginTime(Long userId);

    void delete(ExtendData extendData);
    /**
     * 根据邮箱获取用户ID如果用户不存在，则创建新用户
     *
     * @param request    包含登录信息的请求对象，用于新用户注册时的数据传递
     * @param extendData 扩展数据，可能包含额外的用户信息或上下文数据
     * @return 用户ID，现有用户或新注册用户的唯一标识
     */
    public Long getUserByEmail(EmailLoginRequest request, ExtendData extendData);
    /**
     * 获取登录响应信息
     *
     * @param userId     用户ID，用于获取用户信息和生成登录令牌
     * @param extendData 扩展数据，包含设备和操作系统信息，用于生成令牌和记录登录信息
     * @return 登录响应对象，包含用户信息和登录令牌
     */
    public LoginRes getLoginRes(Long userId, ExtendData extendData);
    /**
     * 通过Google ID获取用户信息
     * 如果用户已存在，则直接返回用户ID；如果用户不存在，则创建新用户并返回新用户的ID
     *
     * @param googleUserInfo  Google用户信息对象，包含subject、email、name等信息
     * @param request    Google登录请求对象，包含登录所需的信息
     * @param extendData 扩展数据对象，用于传递额外的请求参数
     * @return 用户ID
     */
    public Long getUserByGoogle(GoogleUserInfo googleUserInfo, GoogleLoginRequest request, ExtendData extendData);

}

