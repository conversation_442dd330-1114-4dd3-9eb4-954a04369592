package com.letu.solutions.customer.service;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.customer.request.WithdrawApplyReq;
import com.letu.solutions.model.customer.request.WithdrawEstimateReq;
import com.letu.solutions.model.customer.response.WithdrawEstimateRes;

import java.math.BigDecimal;

/**
 * 提现服务类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface WithdrawService {

    /**
     * 预计到账金额计算
     * @return 预计到账信息
     */
    WithdrawEstimateRes estimateWithdraw(String network, BigDecimal amount);

    /**
     * 提现申请
     * @param request 提现申请请求
     * @param user 用户
     * @return 申请结果
     */
    Boolean applyWithdraw(WithdrawApplyReq request, ExtendData user);
}
