package com.letu.solutions.customer.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.MessageLetterQueryService;
import com.letu.solutions.model.cms.request.cms.MessageLetterListReq;
import com.letu.solutions.model.cms.response.cms.MessageLetterPageRes;
import com.letu.solutions.model.cms.response.cms.MessageLetterDetailRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 站内信
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/customer")
@RequiredArgsConstructor
@Validated
public class MessageLetterController {

    private final MessageLetterQueryService MessageLetterQueryService;

//    /**
//     * 站内信 分页查询
//     */
//    @GetMapping("/messageLetter/page")
//    public R<Page<MessageLetterPageRes>> loadPage(MessageLetterListReq request, @RequestHeader ExtendData extendData) {
//        return R.success(MessageLetterQueryService.selectBasicPage(request.getPage(), request, extendData));
//    }

    /**
     * 站内信 列表查询
     */
    @GetMapping("/messageLetter/list")
    public R<List<MessageLetterPageRes>> loadList(MessageLetterListReq request, @RequestHeader ExtendData extendData) {
        return R.success(MessageLetterQueryService.selectBasicList(request, extendData));
    }

    /**
     * 站内信 根据id查询
     */
    @GetMapping("/messageLetter/selectById")
    public R<MessageLetterDetailRes> selectById(@RequestParam("id") Long id, @RequestHeader ExtendData extendData) {
        return R.success(MessageLetterQueryService.selectByIdBasic(id, extendData));
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/messageLetter/markRead")
    public R<Boolean> markAsRead(@RequestParam Long messageId, @RequestHeader ExtendData extendData) {
        return R.success(MessageLetterQueryService.markAsRead(messageId, extendData));
    }

    /**
     * 批量标记消息为已读
     */
    @PostMapping("/messageLetter/batchMarkRead")
    public R<Boolean> batchMarkAsRead(@RequestBody List<Long> messageIds, @RequestHeader ExtendData extendData) {
        return R.success(MessageLetterQueryService.batchMarkAsRead(messageIds, extendData));
    }

    /**
     * 查询用户未读消息数量
     */
    @GetMapping("/messageLetter/unreadCount")
    public R<Integer> getUnreadCount(@RequestHeader ExtendData extendData) {
        return R.success(MessageLetterQueryService.getUnreadCount(extendData));
    }
}