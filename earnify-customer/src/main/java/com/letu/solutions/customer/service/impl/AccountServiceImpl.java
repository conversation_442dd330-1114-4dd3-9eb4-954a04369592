package com.letu.solutions.customer.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.customer.mapper.UserAccountBalanceMapper;
import com.letu.solutions.customer.service.AccountService;
import com.letu.solutions.model.customer.response.AccountBalanceRes;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.util.constants.CurrencyConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;

/**
 * 用户账户服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountServiceImpl extends ServiceImpl<UserAccountBalanceMapper, UserAccountBalance> implements AccountService {

    private final UserAccountBalanceMapper userAccountBalanceMapper;

    @Override
    public AccountBalanceRes getAccountBalance(ExtendData extendData) {
        return getAccountBalanceByCurrency(CurrencyConstants.COIN_USDT, extendData);
    }

    @Override
    public AccountBalanceRes getAccountBalanceByCurrency(String currency, ExtendData extendData) {
        Long userId = extendData.getAuthentication().getUserId();
        Assert.notNull(userId, "用户ID不能为空");

        log.info("查询用户账户余额，用户ID：{}，币种：{}", userId, currency);

        // 查询用户账户余额
        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(UserAccountBalance::getUserId, userId)
                .eq(UserAccountBalance::getCurrency, currency);

        UserAccountBalance userAccountBalance = userAccountBalanceMapper.selectOne(queryWrapper);

        // 如果账户不存在，返回零余额
        if (ObjectUtil.isNull(userAccountBalance)) {
            log.info("用户账户不存在，返回零余额，用户ID：{}，币种：{}", userId, currency);
            return AccountBalanceRes.builder()
                    .userId(userId)
                    .currency(currency)
                    .availableAmount(BigDecimal.ZERO)
                    .frozenAmount(BigDecimal.ZERO)
                    .totalAmount(BigDecimal.ZERO)
                    .build();
        }

        // 计算总余额
        BigDecimal totalAmount = userAccountBalance.getAvailableAmount()
                .add(userAccountBalance.getFrozenAmount());

        AccountBalanceRes result = AccountBalanceRes.builder()
                .userId(userId)
                .currency(currency)
                .availableAmount(userAccountBalance.getAvailableAmount())
                .frozenAmount(userAccountBalance.getFrozenAmount())
                .totalAmount(totalAmount)
                .build();

        log.info("查询用户账户余额成功，用户ID：{}，币种：{}，可用余额：{}，冻结余额：{}，总余额：{}", 
                userId, currency, result.getAvailableAmount(), result.getFrozenAmount(), result.getTotalAmount());

        return result;
    }
}
