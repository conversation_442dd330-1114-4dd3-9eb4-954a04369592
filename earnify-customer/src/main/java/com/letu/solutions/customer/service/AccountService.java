package com.letu.solutions.customer.service;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.customer.response.AccountBalanceRes;

/**
 * 用户账户服务接口
 *
 * <AUTHOR>
 */
public interface AccountService {

    /**
     * 获取用户账户余额信息（默认USDT）
     *
     * @param extendData 扩展数据
     * @return 账户余额信息
     */
    AccountBalanceRes getAccountBalance(ExtendData extendData);

    /**
     * 获取用户账户余额信息（指定币种）
     *
     * @param currency 币种
     * @param extendData 扩展数据
     * @return 账户余额信息
     */
    AccountBalanceRes getAccountBalanceByCurrency(String currency, ExtendData extendData);
}
