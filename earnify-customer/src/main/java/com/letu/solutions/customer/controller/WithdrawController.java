package com.letu.solutions.customer.controller;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.WithdrawService;
import com.letu.solutions.model.customer.request.WithdrawApplyReq;
import com.letu.solutions.model.customer.request.WithdrawEstimateReq;
import com.letu.solutions.model.customer.response.WithdrawEstimateRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.math.BigDecimal;

/**
 * 提现管理
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/customer")
public class WithdrawController {

    private final WithdrawService withdrawService;

    /**
     * 预计到账金额计算
     * 根据用户传入的提现数量和网络，计算预计到账金额
     * 计算公式：到账数量 = 提现金额 - 提现金额 * 手续费率 - 每笔提现费用
     *
     * @return 预计到账信息
     */
    @GetMapping("/withdraw/estimate")
    public R<WithdrawEstimateRes> estimateWithdraw(@RequestParam(defaultValue = "TRC20") String network,
                                                   @RequestParam(defaultValue = "0") BigDecimal amount) {
        return R.success(withdrawService.estimateWithdraw(network,amount));
    }

    /**
     * 提现申请
     * 用户申请提现，需要验证余额、最小最大金额等
     *
     * @param request 提现申请请求
     * @return 提现申请结果
     */
    @PostMapping("/withdraw/apply")
    public R<Boolean> applyWithdraw(@Valid @RequestBody WithdrawApplyReq request, @RequestHeader ExtendData extendData) {
        return R.success(withdrawService.applyWithdraw(request, extendData));
    }
}
