package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.customer.mapper.MessageLetterQueryMapper;
import com.letu.solutions.customer.service.MessageLetterQueryService;
import com.letu.solutions.model.entity.cms.MessageLetter;
import com.letu.solutions.model.cms.request.cms.MessageLetterListReq;
import com.letu.solutions.model.cms.response.cms.MessageLetterPageRes;
import com.letu.solutions.model.cms.response.cms.MessageLetterDetailRes;
import com.letu.solutions.util.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 站内信查询服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageLetterQueryServiceImpl extends ServiceImpl<MessageLetterQueryMapper, MessageLetter> implements MessageLetterQueryService {

    private final MessageLetterQueryMapper MessageLetterQueryMapper;
    private final TransactionTemplate transactionTemplate;

    @Override
    public Page<MessageLetterPageRes> selectBasicPage(Page<MessageLetter> page, MessageLetterListReq request, ExtendData extendData) {
        Long userId = extendData.getAuthentication().getUserId();

        // 动态构建查询条件
        LambdaQueryWrapper<MessageLetter> queryWrapper = Wrappers.<MessageLetter>lambdaQuery()
                .eq(MessageLetter::getUserId, userId)
                .eq(ObjectUtil.isNotEmpty(request.getId()), MessageLetter::getId, request.getId())
                .like(ObjectUtil.isNotEmpty(request.getTitle()), MessageLetter::getTitle, request.getTitle())
                .eq(ObjectUtil.isNotEmpty(request.getReadStatus()), MessageLetter::getReadStatus, request.getReadStatus())
                .ge(ObjectUtil.isNotEmpty(request.getBeginDate()), MessageLetter::getCreateTime, request.getBeginDate())
                .le(ObjectUtil.isNotEmpty(request.getEndDate()), MessageLetter::getCreateTime, request.getEndDate())
                .orderByDesc(MessageLetter::getCreateTime);

        Page<MessageLetter> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, MessageLetterPageRes.class);
    }

    @Override
    public List<MessageLetterPageRes> selectBasicList(MessageLetterListReq request, ExtendData extendData) {
        Long userId = extendData.getAuthentication().getUserId();

        LambdaQueryWrapper<MessageLetter> queryWrapper = Wrappers.<MessageLetter>lambdaQuery()
                .eq(MessageLetter::getUserId, userId)
                .eq(ObjectUtil.isNotEmpty(request.getReadStatus()), MessageLetter::getReadStatus, request.getReadStatus())
                .orderByDesc(MessageLetter::getCreateTime);

        List<MessageLetter> basicList = baseMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(basicList, MessageLetterPageRes.class);
    }

    @Override
    public MessageLetterDetailRes selectByIdBasic(Long id, ExtendData extendData) {
        Long userId = extendData.getAuthentication().getUserId();

        MessageLetter record = baseMapper.selectOne(
                Wrappers.<MessageLetter>lambdaQuery()
                        .eq(MessageLetter::getId, id)
                        .eq(MessageLetter::getUserId, userId)
        );

        Assert.notNull(record, "站内信不存在或无权限访问");
        return BeanUtil.copyProperties(record, MessageLetterDetailRes.class);
    }

    @Override
    public boolean markAsRead(Long messageId, ExtendData extendData) {
        Assert.notNull(messageId, "消息ID不能为空");
        Long userId = extendData.getAuthentication().getUserId();

        return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            int updateCount = MessageLetterQueryMapper.markAsRead(messageId, userId);
            log.info("标记消息为已读，消息ID：{}，用户ID：{}，更新数量：{}", messageId, userId, updateCount);
            return updateCount > 0;
        }));
    }

    @Override
    public boolean batchMarkAsRead(List<Long> messageIds, ExtendData extendData) {
        Assert.notEmpty(messageIds, "消息ID列表不能为空");
        Long userId = extendData.getAuthentication().getUserId();

        return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            int updateCount = MessageLetterQueryMapper.batchMarkAsRead(userId, messageIds);
            log.info("批量标记消息为已读，用户ID：{}，消息数量：{}，更新数量：{}", userId, messageIds.size(), updateCount);
            return updateCount > 0;
        }));
    }

    @Override
    public int getUnreadCount(ExtendData extendData) {
        Long userId = extendData.getAuthentication().getUserId();
        return MessageLetterQueryMapper.countUnreadByUserId(userId);
    }
}