package com.letu.solutions.customer.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.utils.LanguageUtil;
import com.letu.solutions.core.configuration.InviteConfiguration;
import com.letu.solutions.customer.service.LoginService;
import com.letu.solutions.customer.service.UserService;
import com.letu.solutions.customer.service.UserTransactionBillService;
import com.letu.solutions.model.customer.request.UserAssetListReq;
import com.letu.solutions.model.customer.response.UserAssetPageRes;
import com.letu.solutions.model.request.user.UserUpdateHeadReq;
import com.letu.solutions.model.request.user.UserUpdateInfoReq;
import com.letu.solutions.model.request.user.UserUpdateNameReq;
import com.letu.solutions.model.response.login.UserResponse;
import com.letu.solutions.model.response.user.ShareDownloadRes;
import com.letu.solutions.model.response.user.UserUpdateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 用户相关/用户相关查询
 *
 * <AUTHOR>
 * @date 2023年03月22日 9:48
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class UserController {
    private final UserService userService;
    private final LoginService loginService;
    private final InviteConfiguration inviteConfiguration;
    private final UserTransactionBillService userTransactionBillService;

    /**
     * 用户基础信息查询
     */
    @GetMapping("/user/baseUserInfo")
    public R<UserResponse> baseUserInfo(@RequestHeader ExtendData extendData) {
        loginService.refreshToken(extendData);
        return R.success(userService.baseUserInfo(extendData.getUserId()));
    }
//    /**
//     * 测试汉语话
//     *
//     */
//    @GetMapping("/user/test.e")
//    public R<String> test() {
//        return R.success("123","$状态不可为空");
//    }

//    /**
//     * 修改用户登录密码
//     */
//    @PostMapping("/user/updatePwd.e")
//    public R<Void> updatePwd(@Validated @RequestBody PwdUpdateReq body, @RequestHeader ExtendData extendData) {
//        Assert.notEmpty(body.getPhone(), "用户手机号不可为空");
//        Assert.isTrue(body.getPwd().length() >= 6, "密码长度必须大于6位");
//        Assert.isTrue(body.getPwd().length() <= 18, "密码长度必须小于18位");
//        String smsToken = String.format(CacheConstant.smsCheckKey, MessageEnum.MSG_UPDATE_PASSWORD, body.getPhone());
//        String uuid = stringRedisTemplate.opsForValue().get(smsToken);
//        if (StrUtil.isEmpty(uuid) || !body.getUuid().equals(uuid)) {
//            throw new ThrowException("#令牌已失效");
//        }
//        stringRedisTemplate.delete(smsToken);
//        Long userId = userService.updatePwd(body, extendData);
//        if (null != userId) {
//            // 用户登录态踢掉
//            tokenUtil.removeToken(BusinessTypeEnum.novel_customer, userId);
//        }
//        return R.success();
//    }

    /**
     * 修改用户头像
     */
    @PostMapping("/user/updateHeadImg")
    public R<Boolean> updateHeadImg(@Validated @RequestBody UserUpdateHeadReq body, @RequestHeader ExtendData extendData) {
        return R.success(userService.updateHead(body, extendData));
    }

    /**
     * 修改用户昵称
     */
    @PostMapping("/user/updateName")
    public R<Boolean> updateName(@Validated @RequestBody UserUpdateNameReq body, @RequestHeader ExtendData extendData) {
        return R.success(userService.updateName(body, extendData));
    }

    /**
     * 修改用户信息
     */
    @PostMapping("/user/updateBase")
    public R<Boolean> updateBase(@Validated @RequestBody UserUpdateInfoReq body, @RequestHeader ExtendData extendData) {
        UserUpdateResult result = userService.updateBase(body, extendData);
        if (result.isSuccess()) {
            return R.success(true);
        } else {
            return R.fail(result.getErrorMessage());
        }
    }

    /**
     * 获取邀请码
     */
    @GetMapping("/user/loadInvite")
    public R<String> loadInvite(@RequestHeader ExtendData extendData) {
        String inviteCode = userService.getInviteCode(extendData.getUserId());
        if (inviteCode == null) {
            return R.fail(LanguageUtil.trans(I18nConstants.INVITE_CODE_GET_FAILED));
        }
        return R.success(inviteCode);

    }

    /**
     * 获取邀请码和Url
     */
    @GetMapping("/user/loadInviteAndUrl")
    public R<ShareDownloadRes> loadInviteAndUrl(@RequestHeader ExtendData extendData) {
        String inviteCode = userService.getInviteCode(extendData.getUserId());
        if (inviteCode == null) {
            return R.fail(LanguageUtil.trans(I18nConstants.INVITE_CODE_GET_FAILED));
        }
        
        // 构建邀请链接URL
        String inviteUrl = inviteConfiguration.getUrl() + "?code=" + inviteCode;
        
        return R.success(ShareDownloadRes.builder()
                .url(inviteUrl)
                .inviteCode(inviteCode)
                .build());

    }

    /**
     * 用户注销
     */
    @GetMapping("/user/logOff")
    public R<Void> logOff(@RequestHeader ExtendData extendData) {
        userService.loginOut(extendData);
        return R.success();
    }

    /**
     * 用户注销（用户删除）
     */
    @PostMapping("/user/delete")
    public R<Void> delete(@RequestHeader ExtendData extendData) {
        userService.delete(extendData);
        return R.success();
    }

    /**
     * 分页展示当前用户资产
     * 返回用户的资产变动记录，包括时间、初始数量、变动数量、方向、余额等信息
     *
     * @param request 查询请求参数
     * @return 用户资产分页数据
     */
    @GetMapping("/userAsset/page")
    public R<Page<UserAssetPageRes>> getUserAssetPage(UserAssetListReq request, @RequestHeader ExtendData extendData) {
        Long userId = extendData.getUserId();

        return R.success(userTransactionBillService.getUserAssetPage(request, userId));
    }
}
