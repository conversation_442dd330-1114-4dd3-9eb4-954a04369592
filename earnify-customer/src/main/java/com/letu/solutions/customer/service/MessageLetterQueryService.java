package com.letu.solutions.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.entity.cms.MessageLetter;
import com.letu.solutions.model.cms.request.cms.MessageLetterListReq;
import com.letu.solutions.model.cms.response.cms.MessageLetterPageRes;
import com.letu.solutions.model.cms.response.cms.MessageLetterDetailRes;

import java.util.List;

/**
 * 站内信查询服务
 *
 * <AUTHOR>
 */
public interface MessageLetterQueryService {

    Page<MessageLetterPageRes> selectBasicPage(Page<MessageLetter> page, MessageLetterListReq request, ExtendData extendData);

    List<MessageLetterPageRes> selectBasicList(MessageLetterListReq request, ExtendData extendData);

    MessageLetterDetailRes selectByIdBasic(Long id, ExtendData extendData);

    boolean markAsRead(Long messageId, ExtendData extendData);

    boolean batchMarkAsRead(List<Long> messageIds, ExtendData extendData);

    int getUnreadCount(ExtendData extendData);
}