package com.letu.solutions.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.MessageLetter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 站内信查询Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MessageLetterQueryMapper extends BaseMapper<MessageLetter> {

    /**
     * 分页查询用户站内信
     */
    @Select("""
            SELECT id, user_id, title, content, read_status, create_time, update_time
            FROM message_letter 
            WHERE user_id = #{userId} 
            ORDER BY create_time DESC
            """)
    Page<MessageLetter> selectPageByUserId(Page<MessageLetter> page, Long userId);

    /**
     * 标记消息为已读
     */
    @Update("""
            UPDATE message_letter 
            SET read_status = 'read', update_time = NOW() 
            WHERE id = #{messageId} AND user_id = #{userId}
            """)
    int markAsRead(Long messageId, Long userId);

    /**
     * 批量标记消息为已读
     */
    @Update("""
            <script>
            UPDATE message_letter 
            SET read_status = 'read', update_time = NOW() 
            WHERE user_id = #{userId} 
            AND id IN 
            <foreach collection="messageIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            </script>
            """)
    int batchMarkAsRead(Long userId, List<Long> messageIds);

    /**
     * 查询用户未读消息数量
     */
    @Select("""
            SELECT COUNT(*) 
            FROM message_letter 
            WHERE user_id = #{userId} AND read_status = 'unread'
            """)
    int countUnreadByUserId(Long userId);
}