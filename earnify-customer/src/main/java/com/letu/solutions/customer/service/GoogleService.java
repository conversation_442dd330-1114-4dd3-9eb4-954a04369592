package com.letu.solutions.customer.service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.letu.solutions.core.config.ThirdloginConfiguration;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.enums.OsEnum;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.HeaderDto;
import com.letu.solutions.customer.model.GoogleUserInfo;
import com.letu.solutions.model.request.login.GoogleLoginRequest;
import com.letu.solutions.model.response.login.LoginRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @Author: jack ma
 * @CreateTime: 2025-04-28  18:33
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleService {

    private final ThirdloginConfiguration thirdloginConfiguration;

    private final UserService userService;

    /**
     * 处理谷歌登录
     *
     * @param request    谷歌登录请求参数
     * @param extendData 扩展数据
     * @return LoginResponse
     */
    public LoginRes googleLogin(GoogleLoginRequest request, ExtendData extendData) {
        HeaderDto headerDto = extendData.getHeaderDto();
        OsEnum os = headerDto.getOs();
        if (os == null) {
            throw new ThrowException(I18nConstants.PLATFORM_NOT_SUPPORT);
        }
        if (os == OsEnum.web) {
            GoogleIdTokenVerifier verifier = this.getGoogleIdTokenVerifier(os);
            GoogleUserInfo googleUserInfo = this.getSubject(verifier, request.getToken());
            Long userId = userService.getUserByGoogle(googleUserInfo, request, extendData);
            return userService.getLoginRes(userId, extendData);
        } else {
            throw new ThrowException(I18nConstants.PLATFORM_NOT_SUPPORT);
        }

    }

    public GoogleIdTokenVerifier getGoogleIdTokenVerifier(OsEnum os) {
        String clientId;
        if (os == OsEnum.web) {
            clientId = thirdloginConfiguration.getGoogleClientH5();
            log.info("使用H5 Google Client ID: {}", clientId);
            return new GoogleIdTokenVerifier.Builder(new NetHttpTransport(), new GsonFactory())
                    .setAudience(Collections.singletonList(clientId))
                    .build();
        } else {
            clientId = thirdloginConfiguration.getGoogleClientAndroid();
            log.info("使用Android Google Client ID: {}", clientId);
            return new GoogleIdTokenVerifier.Builder(new NetHttpTransport(), new GsonFactory())
                    .setAudience(Collections.singletonList(clientId))
                    .build();
        }
    }

    public GoogleUserInfo getSubject(GoogleIdTokenVerifier verifier, String token) {
        try {
            log.info("开始验证Google Token，token长度: {}", token != null ? token.length() : 0);
            
            // 检查token是否为空或无效
            if (token == null || token.trim().isEmpty()) {
                log.error("Google Token为空或无效");
                throw new ThrowException(I18nConstants.INVALID_GOOGLE_TOKEN);
            }
            
            // 记录token的前20个字符用于调试（不记录完整token以保护隐私）
            String tokenPreview = token.length() > 20 ? token.substring(0, 20) + "..." : token;
            log.info("Google Token预览: {}", tokenPreview);
            
            GoogleIdToken idToken = verifier.verify(token);
            log.info("Google Token验证结果: {}", idToken != null ? "成功" : "失败");
            
            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();
                String subject = payload.getSubject();
                String email = (String) payload.get("email");
                String name = (String) payload.get("name");
                String picture = (String) payload.get("picture");
                
                log.info("Google用户信息 - Subject: {}, Email: {}, Name: {}", subject, email, name);
                
                return GoogleUserInfo.builder()
                        .subject(subject)
                        .email(email)
                        .name(name)
                        .picture(picture)
                        .build();
            } else {
                log.error("Google Token验证失败：idToken为null");
                throw new ThrowException(I18nConstants.INVALID_GOOGLE_TOKEN);
            }
        } catch (Exception e) {
            log.error("谷歌登录验证失败，详细错误: {}", e.getMessage(), e);
            throw new ThrowException(I18nConstants.GOOGLE_VERIFICATION_FAIL);
        }
    }
}
